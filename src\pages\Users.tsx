import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Users as UsersIcon, UserPlus, Settings, Shield, Trash2, KeyRound, Lock } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { useAuth, User } from '@/contexts/LocalAuthContext';

const getRoleLabel = (role: string) => {
  const roleLabels = {
    admin: 'مدير النظام',
    planning: 'التخطيط',
    production: 'الإنتاج',
    quality: 'الجودة',
    warehouse: 'المخزن'
  };
  return roleLabels[role as keyof typeof roleLabels] || role;
};

const getRoleBadgeVariant = (role: string) => {
  const variants = {
    admin: 'destructive' as const,
    planning: 'default' as const,
    production: 'secondary' as const,
    quality: 'outline' as const,
    warehouse: 'secondary' as const
  };
  return variants[role as keyof typeof variants] || 'outline' as const;
};

export default function Users() {
  const { users, addUser, updateUser, deleteUser, currentUser } = useAuth();
  const [isUserDialogOpen, setIsUserDialogOpen] = useState(false);
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [passwordResetUser, setPasswordResetUser] = useState<User | null>(null);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const { toast } = useToast();

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsUserDialogOpen(true);
  };

  const handleAddNewUser = () => {
    setEditingUser(null);
    setIsUserDialogOpen(true);
  };

  const handleDeleteUser = (userId: string) => {
    const success = deleteUser(userId);
    if (success) {
      toast({
        title: "تم حذف المستخدم",
        description: "تم حذف المستخدم بنجاح",
      });
    } else {
      toast({
        title: "خطأ",
        description: "فشل في حذف المستخدم",
        variant: "destructive",
      });
    }
  };

  const handleResetPassword = (user: User) => {
    setPasswordResetUser(user);
    setNewPassword('');
    setConfirmPassword('');
    setPasswordError('');
    setIsPasswordDialogOpen(true);
  };

  const handleSavePassword = () => {
    if (!passwordResetUser) return;

    if (newPassword !== confirmPassword) {
      setPasswordError('كلمات المرور غير متطابقة');
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    // Update user with new password
    const success = updateUser(passwordResetUser.id, {}, newPassword);

    if (success) {
      toast({
        title: "تم تغيير كلمة المرور",
        description: "تم تغيير كلمة المرور بنجاح",
      });
      setIsPasswordDialogOpen(false);
    } else {
      setPasswordError('فشل في تغيير كلمة المرور');
    }
  };

  const handleSaveUser = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'> = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      role: formData.get('role') as User['role'],
      department: formData.get('department') as string,
      status: 'active', // Default status
    };

    const password = formData.get('password') as string;

    let success = false;

    if (editingUser) {
      // Update existing user
      success = updateUser(editingUser.id, userData, password || undefined);
    } else {
      // Add new user
      if (!password || password.length < 6) {
        toast({
          title: "خطأ",
          description: "كلمة المرور يجب أن تكون 6 أحرف على الأقل",
          variant: "destructive",
        });
        return;
      }
      success = addUser(userData, password);
    }

    if (success) {
      toast({
        title: editingUser ? "تم تحديث المستخدم" : "تم إضافة المستخدم",
        description: editingUser ? "تم تحديث بيانات المستخدم بنجاح" : "تم إضافة المستخدم الجديد بنجاح",
      });
      setIsUserDialogOpen(false);
      setEditingUser(null);
    } else {
      toast({
        title: "خطأ",
        description: "فشل في حفظ بيانات المستخدم",
        variant: "destructive",
      });
    }
  };


  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">إدارة المستخدمين</h1>
          <p className="text-muted-foreground mt-2">
            إدارة المستخدمين والصلاحيات في النظام
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleAddNewUser}>
            <UserPlus className="ml-2 h-4 w-4" />
            إضافة مستخدم جديد
          </Button>
          <Button variant="outline" onClick={() => setIsPermissionsDialogOpen(true)}>
            <Settings className="ml-2 h-4 w-4" />
            إعدادات الصلاحيات
          </Button>
        </div>
      </div>

      {/* User Form Dialog */}
      <Dialog open={isUserDialogOpen} onOpenChange={setIsUserDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{editingUser ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}</DialogTitle>
            <DialogDescription>
              {editingUser ? 'قم بتحديث بيانات المستخدم.' : 'أدخل بيانات المستخدم الجديد.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSaveUser}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  الاسم
                </Label>
                <Input id="name" name="name" defaultValue={editingUser?.name} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  البريد الإلكتروني
                </Label>
                <Input id="email" name="email" type="email" defaultValue={editingUser?.email} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password" className="text-right">
                  كلمة المرور
                </Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder={editingUser ? 'اتركها فارغة لعدم التغيير' : 'كلمة المرور الجديدة'}
                  className="col-span-3"
                  required={!editingUser}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="role" className="text-right">
                  الصلاحية
                </Label>
                <Select name="role" defaultValue={editingUser?.role || 'planning'} required>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="اختر صلاحية" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">مدير النظام</SelectItem>
                    <SelectItem value="planning">التخطيط</SelectItem>
                    <SelectItem value="production">الإنتاج</SelectItem>
                    <SelectItem value="quality">الجودة</SelectItem>
                    <SelectItem value="warehouse">المخزن</SelectItem>
                  </SelectContent>
                </Select>
              </div>
               <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="department" className="text-right">
                  القسم
                </Label>
                <Input id="department" name="department" defaultValue={editingUser?.department} className="col-span-3" required />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">حفظ التغييرات</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Password Reset Dialog */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>تغيير كلمة المرور</DialogTitle>
            <DialogDescription>
              {passwordResetUser && `تغيير كلمة المرور للمستخدم: ${passwordResetUser.name}`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="newPassword" className="text-right">
                كلمة المرور الجديدة
              </Label>
              <Input 
                id="newPassword" 
                type="password" 
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="col-span-3" 
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="confirmPassword" className="text-right">
                تأكيد كلمة المرور
              </Label>
              <Input 
                id="confirmPassword" 
                type="password" 
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="col-span-3" 
                required
              />
            </div>
            {passwordError && (
              <div className="text-destructive text-sm">{passwordError}</div>
            )}
          </div>
          <DialogFooter>
            <Button onClick={handleSavePassword}>حفظ كلمة المرور</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Permissions Management Dialog */}
      <Dialog open={isPermissionsDialogOpen} onOpenChange={setIsPermissionsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>إدارة الصلاحيات</DialogTitle>
            <DialogDescription>
              تحكم في صلاحيات المستخدمين للوصول إلى أقسام النظام المختلفة
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="roles">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="roles">الأدوار والصلاحيات</TabsTrigger>
              <TabsTrigger value="access">إعدادات الوصول</TabsTrigger>
            </TabsList>
            <TabsContent value="roles" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">مدير النظام (Admin)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    يمتلك جميع الصلاحيات في النظام بما في ذلك إدارة المستخدمين
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">الوصول الكامل</Badge>
                    <Badge variant="outline">إدارة المستخدمين</Badge>
                    <Badge variant="outline">التقارير</Badge>
                    <Badge variant="outline">إعدادات النظام</Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">التخطيط (Planning)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    إدارة خطط الإنتاج وجدولة العمليات
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">قسم التخطيط</Badge>
                    <Badge variant="outline">عرض التقارير</Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">الإنتاج (Production)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    إدارة عمليات الإنتاج وتسجيل الإنتاجية
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">قسم الإنتاج</Badge>
                    <Badge variant="outline">عرض التقارير</Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">الجودة (Quality)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    مراقبة جودة المنتجات وتسجيل الفحوصات
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">قسم الجودة</Badge>
                    <Badge variant="outline">عرض التقارير</Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">المخزن (Warehouse)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    إدارة المخزون والمواد الخام والمنتجات النهائية
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">قسم المخزن</Badge>
                    <Badge variant="outline">عرض التقارير</Badge>
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="access" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-3">إعدادات الوصول للأقسام</h3>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">قسم التخطيط</h4>
                        <p className="text-sm text-muted-foreground">يتطلب تسجيل دخول بصلاحية التخطيط</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">قسم الإنتاج</h4>
                        <p className="text-sm text-muted-foreground">يتطلب تسجيل دخول بصلاحية الإنتاج</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">قسم الجودة</h4>
                        <p className="text-sm text-muted-foreground">يتطلب تسجيل دخول بصلاحية الجودة</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">قسم المخزن</h4>
                        <p className="text-sm text-muted-foreground">يتطلب تسجيل دخول بصلاحية المخزن</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">إدارة المستخدمين</h4>
                        <p className="text-sm text-muted-foreground">متاح فقط لمدير النظام</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-3">سياسة كلمات المرور</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    إعدادات أمان كلمات المرور في النظام
                  </p>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">الحد الأدنى لطول كلمة المرور</h4>
                        <p className="text-sm text-muted-foreground">6 أحرف على الأقل</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">تغيير إجباري لكلمة المرور</h4>
                        <p className="text-sm text-muted-foreground">كل 90 يوم</p>
                      </div>
                      <Switch checked={false} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">تسجيل محاولات الدخول الفاشلة</h4>
                        <p className="text-sm text-muted-foreground">تسجيل محاولات تسجيل الدخول غير الناجحة</p>
                      </div>
                      <Switch checked={false} disabled />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <DialogFooter>
            <Button onClick={() => setIsPermissionsDialogOpen(false)}>إغلاق</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Password Reset Dialog */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>تغيير كلمة المرور</DialogTitle>
            <DialogDescription>
              {passwordResetUser && `تغيير كلمة المرور للمستخدم: ${passwordResetUser.name}`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-2">
              <Label htmlFor="newPassword" className="text-right">
                كلمة المرور الجديدة
              </Label>
              <Input 
                id="newPassword" 
                type="password" 
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="col-span-3" 
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="confirmPassword" className="text-right">
                تأكيد كلمة المرور
              </Label>
              <Input 
                id="confirmPassword" 
                type="password" 
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="col-span-3" 
                required
              />
            </div>
            {passwordError && (
              <div className="text-destructive text-sm">{passwordError}</div>
            )}
          </div>
          <DialogFooter>
            <Button onClick={handleSavePassword}>حفظ كلمة المرور</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Permissions Management Dialog */}
      <Dialog open={isPermissionsDialogOpen} onOpenChange={setIsPermissionsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>إدارة الصلاحيات</DialogTitle>
            <DialogDescription>
              تحكم في صلاحيات المستخدمين للوصول إلى أقسام النظام المختلفة
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="roles">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="roles">الأدوار والصلاحيات</TabsTrigger>
              <TabsTrigger value="access">إعدادات الوصول</TabsTrigger>
            </TabsList>
            <TabsContent value="roles" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">مدير النظام (Admin)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    يمتلك جميع الصلاحيات في النظام بما في ذلك إدارة المستخدمين
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">الوصول الكامل</Badge>
                    <Badge variant="outline">إدارة المستخدمين</Badge>
                    <Badge variant="outline">التقارير</Badge>
                    <Badge variant="outline">إعدادات النظام</Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">التخطيط (Planning)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    إدارة خطط الإنتاج وجدولة العمليات
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">قسم التخطيط</Badge>
                    <Badge variant="outline">عرض التقارير</Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">الإنتاج (Production)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    إدارة عمليات الإنتاج وتسجيل الإنتاجية
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">قسم الإنتاج</Badge>
                    <Badge variant="outline">عرض التقارير</Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">الجودة (Quality)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    مراقبة جودة المنتجات وتسجيل الفحوصات
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">قسم الجودة</Badge>
                    <Badge variant="outline">عرض التقارير</Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">المخزن (Warehouse)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    إدارة المخزون والمواد الخام والمنتجات النهائية
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Badge variant="outline">قسم المخزن</Badge>
                    <Badge variant="outline">عرض التقارير</Badge>
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="access" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-3">إعدادات الوصول للأقسام</h3>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">قسم التخطيط</h4>
                        <p className="text-sm text-muted-foreground">يتطلب تسجيل دخول بصلاحية التخطيط</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">قسم الإنتاج</h4>
                        <p className="text-sm text-muted-foreground">يتطلب تسجيل دخول بصلاحية الإنتاج</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">قسم الجودة</h4>
                        <p className="text-sm text-muted-foreground">يتطلب تسجيل دخول بصلاحية الجودة</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">قسم المخزن</h4>
                        <p className="text-sm text-muted-foreground">يتطلب تسجيل دخول بصلاحية المخزن</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">إدارة المستخدمين</h4>
                        <p className="text-sm text-muted-foreground">متاح فقط لمدير النظام</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-3">سياسة كلمات المرور</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    إعدادات أمان كلمات المرور في النظام
                  </p>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">الحد الأدنى لطول كلمة المرور</h4>
                        <p className="text-sm text-muted-foreground">6 أحرف على الأقل</p>
                      </div>
                      <Switch checked={true} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">تغيير إجباري لكلمة المرور</h4>
                        <p className="text-sm text-muted-foreground">كل 90 يوم</p>
                      </div>
                      <Switch checked={false} disabled />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">تسجيل محاولات الدخول الفاشلة</h4>
                        <p className="text-sm text-muted-foreground">تسجيل محاولات تسجيل الدخول غير الناجحة</p>
                      </div>
                      <Switch checked={false} disabled />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <DialogFooter>
            <Button onClick={() => setIsPermissionsDialogOpen(false)}>إغلاق</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* إحصائيات المستخدمين */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المستخدمين</CardTitle>
            <UsersIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.length}</div>
            <p className="text-xs text-muted-foreground">
              {users.filter(u => u.status === 'active').length} نشط
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المديرين</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {users.filter(u => u.role === 'admin').length}
            </div>
            <p className="text-xs text-muted-foreground">
              صلاحية كاملة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأقسام</CardTitle>
            <UsersIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(users.map(u => u.department)).size}
            </div>
            <p className="text-xs text-muted-foreground">
              قسم مختلف
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">آخر نشاط</CardTitle>
            <UsersIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">اليوم</div>
            <p className="text-xs text-muted-foreground">
              آخر تسجيل دخول
            </p>
          </CardContent>
        </Card>
      </div>

      {/* قائمة المستخدمين */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة المستخدمين</CardTitle>
          <CardDescription>
            جميع المستخدمين المسجلين في النظام مع صلاحياتهم
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Avatar>
                      <AvatarFallback>
                        {user.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <h3 className="font-semibold">{user.name}</h3>
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {getRoleLabel(user.role)}
                    </Badge>
                    
                    <Badge variant={user.status === 'active' ? 'default' : 'secondary'}>
                      {user.status === 'active' ? 'نشط' : 'غير نشط'}
                    </Badge>
                    
                    <Button variant="outline" size="sm" onClick={() => handleResetPassword(user)}>
                      <KeyRound className="h-4 w-4" />
                    </Button>
                    
                    <Button variant="outline" size="sm" onClick={() => handleEditUser(user)}>
                      <Settings className="h-4 w-4" />
                    </Button>
                    
                    {user.id !== currentUser?.id && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>
                            <AlertDialogDescription>
                              هذا الإجراء سيقوم بحذف المستخدم بشكل نهائي. لا يمكن التراجع عن هذا الإجراء.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteUser(user.id)}>
                              حذف
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </div>
                </div>
                
                <div className="mt-3 pt-3 border-t grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">القسم:</span>
                    <p className="font-medium">{user.department}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">آخر تسجيل دخول:</span>
                    <p className="font-medium">
                      {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-EG') : 'لم يسجل الدخول بعد'}
                    </p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">معرف المستخدم:</span>
                    <p className="font-medium">{user.id}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
