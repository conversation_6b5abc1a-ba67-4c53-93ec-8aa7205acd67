import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { UserPlus, Settings, Shield, Activity, Users as UsersIcon } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useAuth, User } from '@/contexts/LocalAuthContext';
import { UserManagementTable } from '@/components/admin/UserManagementTable';
import { UserStatsCards } from '@/components/admin/UserStatsCards';
import { UserFormDialog } from '@/components/admin/UserFormDialog';
import { permissionService } from '@/services/permissionService';
import { auditService } from '@/services/auditService';



export default function Users() {
  const {
    users,
    roles,
    addUser,
    updateUser,
    deleteUser,
    resetPassword,
    lockUser,
    unlockUser,
    currentUser
  } = useAuth();

  const [isUserDialogOpen, setIsUserDialogOpen] = useState(false);
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [passwordResetUser, setPasswordResetUser] = useState<User | null>(null);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const { toast } = useToast();

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsUserDialogOpen(true);
  };

  const handleAddNewUser = () => {
    setEditingUser(null);
    setIsUserDialogOpen(true);
  };

  const handleDeleteUser = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setUserToDelete(user);
      setIsDeleteDialogOpen(true);
    }
  };

  const confirmDeleteUser = () => {
    if (!userToDelete) return;

    const result = deleteUser(userToDelete.id);
    if (result.success) {
      toast({
        title: "تم حذف المستخدم",
        description: result.message || "تم حذف المستخدم بنجاح",
      });
    } else {
      toast({
        title: "خطأ",
        description: result.message || "فشل في حذف المستخدم",
        variant: "destructive",
      });
    }
    setIsDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  const handleResetPassword = (user: User) => {
    setPasswordResetUser(user);
    setNewPassword('');
    setConfirmPassword('');
    setPasswordError('');
    setIsPasswordDialogOpen(true);
  };

  const handleSavePassword = () => {
    if (!passwordResetUser) return;

    if (newPassword !== confirmPassword) {
      setPasswordError('كلمات المرور غير متطابقة');
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    const result = resetPassword(passwordResetUser.id, newPassword);

    if (result.success) {
      toast({
        title: "تم تغيير كلمة المرور",
        description: result.message || "تم تغيير كلمة المرور بنجاح",
      });
      setIsPasswordDialogOpen(false);
      setPasswordResetUser(null);
      setNewPassword('');
      setConfirmPassword('');
      setPasswordError('');
    } else {
      setPasswordError(result.message || 'فشل في تغيير كلمة المرور');
    }
  };

  const handleLockUser = (userId: string) => {
    const result = lockUser(userId);
    toast({
      title: result.success ? "تم قفل المستخدم" : "خطأ",
      description: result.message,
      variant: result.success ? "default" : "destructive",
    });
  };

  const handleUnlockUser = (userId: string) => {
    const result = unlockUser(userId);
    toast({
      title: result.success ? "تم إلغاء قفل المستخدم" : "خطأ",
      description: result.message,
      variant: result.success ? "default" : "destructive",
    });
  };

  const handleSaveUser = (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>, password?: string) => {
    let result;

    if (editingUser) {
      // Update existing user
      result = updateUser(editingUser.id, userData, password);
    } else {
      // Add new user
      if (!password) {
        return { success: false, message: "كلمة المرور مطلوبة للمستخدمين الجدد" };
      }
      result = addUser(userData, password);
    }

    if (result.success) {
      toast({
        title: editingUser ? "تم تحديث المستخدم" : "تم إضافة المستخدم",
        description: result.message || (editingUser ? "تم تحديث بيانات المستخدم بنجاح" : "تم إضافة المستخدم الجديد بنجاح"),
      });
      setEditingUser(null);
    } else {
      toast({
        title: "خطأ",
        description: result.message || "فشل في حفظ بيانات المستخدم",
        variant: "destructive",
      });
    }

    return result;
  };


  if (!currentUser) {
    return <div>جاري التحميل...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">إدارة المستخدمين</h1>
          <p className="text-muted-foreground mt-2">
            إدارة المستخدمين والصلاحيات في النظام
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleAddNewUser}>
            <UserPlus className="ml-2 h-4 w-4" />
            إضافة مستخدم جديد
          </Button>
          <Button variant="outline" onClick={() => setIsPermissionsDialogOpen(true)}>
            <Settings className="ml-2 h-4 w-4" />
            إعدادات الصلاحيات
          </Button>
        </div>
      </div>

      {/* إحصائيات المستخدمين */}
      <UserStatsCards users={users} roles={roles} />

      {/* جدول إدارة المستخدمين */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UsersIcon className="h-5 w-5" />
            قائمة المستخدمين
          </CardTitle>
          <CardDescription>
            إدارة شاملة لجميع المستخدمين في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserManagementTable
            users={users}
            roles={roles}
            currentUser={currentUser}
            onEditUser={handleEditUser}
            onDeleteUser={handleDeleteUser}
            onResetPassword={handleResetPassword}
            onLockUser={handleLockUser}
            onUnlockUser={handleUnlockUser}
          />
        </CardContent>
      </Card>

      {/* User Form Dialog */}
      <UserFormDialog
        open={isUserDialogOpen}
        onOpenChange={setIsUserDialogOpen}
        user={editingUser}
        roles={roles}
        onSave={handleSaveUser}
      />

      {/* Password Reset Dialog */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>تغيير كلمة المرور</DialogTitle>
            <DialogDescription>
              {passwordResetUser && `تغيير كلمة المرور للمستخدم: ${passwordResetUser.name}`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="newPassword" className="text-right">
                كلمة المرور الجديدة
              </Label>
              <Input 
                id="newPassword" 
                type="password" 
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="col-span-3" 
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="confirmPassword" className="text-right">
                تأكيد كلمة المرور
              </Label>
              <Input 
                id="confirmPassword" 
                type="password" 
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="col-span-3" 
                required
              />
            </div>
            {passwordError && (
              <div className="text-destructive text-sm">{passwordError}</div>
            )}
          </div>
          <DialogFooter>
            <Button onClick={handleSavePassword}>حفظ كلمة المرور</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>



      {/* Password Reset Dialog */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>تغيير كلمة المرور</DialogTitle>
            <DialogDescription>
              {passwordResetUser && `تغيير كلمة المرور للمستخدم: ${passwordResetUser.name}`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-2">
              <Label htmlFor="newPassword" className="text-right">
                كلمة المرور الجديدة
              </Label>
              <Input
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="col-span-3"
                required
                dir="ltr"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="confirmPassword" className="text-right">
                تأكيد كلمة المرور
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="col-span-3"
                required
                dir="ltr"
              />
            </div>
            {passwordError && (
              <div className="text-destructive text-sm">{passwordError}</div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPasswordDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleSavePassword}>حفظ كلمة المرور</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الحذف؟</AlertDialogTitle>
            <AlertDialogDescription>
              {userToDelete && (
                <>
                  سيتم حذف المستخدم <strong>{userToDelete.name}</strong> ({userToDelete.email}) نهائياً.
                  <br />
                  هذا الإجراء لا يمكن التراجع عنه.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteUser} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Permissions Management Dialog - Simplified for now */}
      <Dialog open={isPermissionsDialogOpen} onOpenChange={setIsPermissionsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              إدارة الصلاحيات
            </DialogTitle>
            <DialogDescription>
              عرض معلومات الأدوار والصلاحيات في النظام
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="text-center text-muted-foreground">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="font-medium mb-2">إدارة الصلاحيات المتقدمة</h3>
              <p className="text-sm">
                تم تطوير نظام صلاحيات متقدم جديد. سيتم إضافة واجهة إدارة الصلاحيات قريباً.
              </p>
            </div>

            <div className="border rounded-lg p-4 bg-muted/50">
              <h4 className="font-medium mb-2">الميزات الجديدة:</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• نظام أذونات مفصل لكل وحدة</li>
                <li>• أدوار قابلة للتخصيص</li>
                <li>• تسجيل شامل للعمليات</li>
                <li>• إدارة انتهاء الصلاحيات</li>
              </ul>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => setIsPermissionsDialogOpen(false)}>إغلاق</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
