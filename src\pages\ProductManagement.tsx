import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { productionService } from '@/services/productionService';
import { Product, ProductPart, PartOperation, Workshop, Machine } from '@/types/production';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Package, 
  Settings, 
  Wrench,
  Factory,
  Clock,
  Users,
  CheckCircle
} from "lucide-react";

export default function ProductManagement() {
  const [products, setProducts] = useState<Product[]>([]);
  const [workshops, setWorkshops] = useState<Workshop[]>([]);
  const [machines, setMachines] = useState<Machine[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);
  const [isPartDialogOpen, setIsPartDialogOpen] = useState(false);
  const [isOperationDialogOpen, setIsOperationDialogOpen] = useState(false);
  const [selectedPart, setSelectedPart] = useState<ProductPart | null>(null);
  const [activeTab, setActiveTab] = useState('products');
  
  const [productForm, setProductForm] = useState({
    name: '',
    code: '',
    description: '',
    category: '',
    unit: 'قطعة',
    estimatedTime: 0,
    cost: 0
  });

  const [partForm, setPartForm] = useState({
    name: '',
    code: '',
    description: '',
    quantity: 1,
    unit: 'قطعة',
    material: '',
    dimensions: '',
    weight: 0
  });

  const [operationForm, setOperationForm] = useState({
    name: '',
    code: '',
    description: '',
    sequence: 1,
    workshopId: '',
    machineId: '',
    estimatedTime: 0,
    setupTime: 0,
    skillLevel: 'intermediate' as const,
    instructions: '',
    tools: [] as string[]
  });

  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setProducts(productionService.getProducts());
    setWorkshops(productionService.getWorkshops());
    setMachines(productionService.getMachines());
  };

  const handleAddProduct = () => {
    if (!productForm.name || !productForm.code) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    const newProduct = productionService.addProduct({
      ...productForm,
      parts: [],
      isActive: true,
      createdBy: 'current_user' // يجب تمرير المستخدم الحالي
    });

    setProducts(prev => [...prev, newProduct]);
    setIsProductDialogOpen(false);
    resetProductForm();
    
    toast({
      title: "تم إضافة المنتج",
      description: `تم إضافة المنتج ${newProduct.name} بنجاح`,
    });
  };

  const handleAddPart = () => {
    if (!selectedProduct || !partForm.name || !partForm.code) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    const newPart = productionService.addPart({
      ...partForm,
      productId: selectedProduct.id,
      operations: [],
      isActive: true
    });

    // تحديث المنتج المحدد
    const updatedProduct = { ...selectedProduct };
    updatedProduct.parts.push(newPart);
    setSelectedProduct(updatedProduct);
    
    // تحديث قائمة المنتجات
    setProducts(prev => prev.map(p => p.id === selectedProduct.id ? updatedProduct : p));
    
    setIsPartDialogOpen(false);
    resetPartForm();
    
    toast({
      title: "تم إضافة الجزء",
      description: `تم إضافة الجزء ${newPart.name} بنجاح`,
    });
  };

  const handleAddOperation = () => {
    if (!selectedPart || !operationForm.name || !operationForm.workshopId) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    const newOperation = productionService.addOperation({
      ...operationForm,
      partId: selectedPart.id,
      qualityChecks: [],
      tools: operationForm.tools.filter(t => t.trim() !== ''),
      isActive: true
    });

    // تحديث الجزء المحدد
    const updatedPart = { ...selectedPart };
    updatedPart.operations.push(newOperation);
    setSelectedPart(updatedPart);
    
    // تحديث المنتج والقائمة
    if (selectedProduct) {
      const updatedProduct = { ...selectedProduct };
      updatedProduct.parts = updatedProduct.parts.map(p => p.id === selectedPart.id ? updatedPart : p);
      setSelectedProduct(updatedProduct);
      setProducts(prev => prev.map(p => p.id === selectedProduct.id ? updatedProduct : p));
    }
    
    setIsOperationDialogOpen(false);
    resetOperationForm();
    
    toast({
      title: "تم إضافة العملية",
      description: `تم إضافة العملية ${newOperation.name} بنجاح`,
    });
  };

  const resetProductForm = () => {
    setProductForm({
      name: '',
      code: '',
      description: '',
      category: '',
      unit: 'قطعة',
      estimatedTime: 0,
      cost: 0
    });
  };

  const resetPartForm = () => {
    setPartForm({
      name: '',
      code: '',
      description: '',
      quantity: 1,
      unit: 'قطعة',
      material: '',
      dimensions: '',
      weight: 0
    });
  };

  const resetOperationForm = () => {
    setOperationForm({
      name: '',
      code: '',
      description: '',
      sequence: 1,
      workshopId: '',
      machineId: '',
      estimatedTime: 0,
      setupTime: 0,
      skillLevel: 'intermediate',
      instructions: '',
      tools: []
    });
  };

  const getWorkshopName = (workshopId: string) => {
    const workshop = workshops.find(w => w.id === workshopId);
    return workshop?.name || 'غير محدد';
  };

  const getMachineName = (machineId: string) => {
    const machine = machines.find(m => m.id === machineId);
    return machine?.name || 'غير محدد';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">إدارة المنتجات</h1>
          <p className="text-muted-foreground mt-2">
            إدارة المنتجات والأجزاء والعمليات
          </p>
        </div>
        <Button onClick={() => setIsProductDialogOpen(true)}>
          <Plus className="ml-2 h-4 w-4" />
          إضافة منتج جديد
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="products">المنتجات</TabsTrigger>
          <TabsTrigger value="parts">الأجزاء</TabsTrigger>
          <TabsTrigger value="operations">العمليات</TabsTrigger>
        </TabsList>

        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>قائمة المنتجات</CardTitle>
              <CardDescription>
                جميع المنتجات المسجلة في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الكود</TableHead>
                    <TableHead>اسم المنتج</TableHead>
                    <TableHead>الفئة</TableHead>
                    <TableHead>الوحدة</TableHead>
                    <TableHead>عدد الأجزاء</TableHead>
                    <TableHead>الوقت المقدر</TableHead>
                    <TableHead>التكلفة</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-mono">{product.code}</TableCell>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>{product.category}</TableCell>
                      <TableCell>{product.unit}</TableCell>
                      <TableCell>{product.parts.length}</TableCell>
                      <TableCell>{product.estimatedTime} دقيقة</TableCell>
                      <TableCell>{product.cost} ريال</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedProduct(product);
                              setActiveTab('parts');
                            }}
                          >
                            <Package className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedProduct(product);
                              setProductForm({
                                name: product.name,
                                code: product.code,
                                description: product.description || '',
                                category: product.category,
                                unit: product.unit,
                                estimatedTime: product.estimatedTime,
                                cost: product.cost
                              });
                              setIsProductDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="parts" className="space-y-6">
          {selectedProduct ? (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>أجزاء المنتج: {selectedProduct.name}</CardTitle>
                    <CardDescription>
                      إدارة أجزاء المنتج المحدد
                    </CardDescription>
                  </div>
                  <Button onClick={() => setIsPartDialogOpen(true)}>
                    <Plus className="ml-2 h-4 w-4" />
                    إضافة جزء
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الكود</TableHead>
                      <TableHead>اسم الجزء</TableHead>
                      <TableHead>الكمية</TableHead>
                      <TableHead>الوحدة</TableHead>
                      <TableHead>المادة</TableHead>
                      <TableHead>عدد العمليات</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedProduct.parts.map((part) => (
                      <TableRow key={part.id}>
                        <TableCell className="font-mono">{part.code}</TableCell>
                        <TableCell className="font-medium">{part.name}</TableCell>
                        <TableCell>{part.quantity}</TableCell>
                        <TableCell>{part.unit}</TableCell>
                        <TableCell>{part.material || '-'}</TableCell>
                        <TableCell>{part.operations.length}</TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedPart(part);
                              setActiveTab('operations');
                            }}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">اختر منتجاً لعرض أجزائه</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="operations" className="space-y-6">
          {selectedPart ? (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>عمليات الجزء: {selectedPart.name}</CardTitle>
                    <CardDescription>
                      إدارة عمليات الجزء المحدد
                    </CardDescription>
                  </div>
                  <Button onClick={() => setIsOperationDialogOpen(true)}>
                    <Plus className="ml-2 h-4 w-4" />
                    إضافة عملية
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الترتيب</TableHead>
                      <TableHead>اسم العملية</TableHead>
                      <TableHead>الورشة</TableHead>
                      <TableHead>المكنة</TableHead>
                      <TableHead>الوقت المقدر</TableHead>
                      <TableHead>مستوى المهارة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedPart.operations
                      .sort((a, b) => a.sequence - b.sequence)
                      .map((operation) => (
                      <TableRow key={operation.id}>
                        <TableCell>{operation.sequence}</TableCell>
                        <TableCell className="font-medium">{operation.name}</TableCell>
                        <TableCell>{getWorkshopName(operation.workshopId)}</TableCell>
                        <TableCell>{operation.machineId ? getMachineName(operation.machineId) : '-'}</TableCell>
                        <TableCell>{operation.estimatedTime} دقيقة</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {operation.skillLevel === 'beginner' ? 'مبتدئ' :
                             operation.skillLevel === 'intermediate' ? 'متوسط' :
                             operation.skillLevel === 'advanced' ? 'متقدم' : 'خبير'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Wrench className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">اختر جزءاً لعرض عملياته</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Dialog إضافة منتج */}
      <Dialog open={isProductDialogOpen} onOpenChange={setIsProductDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>إضافة منتج جديد</DialogTitle>
            <DialogDescription>
              أدخل بيانات المنتج الجديد
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="product-name">اسم المنتج *</Label>
                <Input
                  id="product-name"
                  value={productForm.name}
                  onChange={(e) => setProductForm({...productForm, name: e.target.value})}
                  placeholder="أدخل اسم المنتج"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="product-code">كود المنتج *</Label>
                <Input
                  id="product-code"
                  value={productForm.code}
                  onChange={(e) => setProductForm({...productForm, code: e.target.value})}
                  placeholder="أدخل كود المنتج"
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="product-description">الوصف</Label>
              <Textarea
                id="product-description"
                value={productForm.description}
                onChange={(e) => setProductForm({...productForm, description: e.target.value})}
                placeholder="أدخل وصف المنتج"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="product-category">الفئة</Label>
                <Input
                  id="product-category"
                  value={productForm.category}
                  onChange={(e) => setProductForm({...productForm, category: e.target.value})}
                  placeholder="أدخل فئة المنتج"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="product-unit">الوحدة</Label>
                <Select value={productForm.unit} onValueChange={(value) => setProductForm({...productForm, unit: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="قطعة">قطعة</SelectItem>
                    <SelectItem value="كيلو">كيلو</SelectItem>
                    <SelectItem value="متر">متر</SelectItem>
                    <SelectItem value="لتر">لتر</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="product-time">الوقت المقدر (دقيقة)</Label>
                <Input
                  id="product-time"
                  type="number"
                  value={productForm.estimatedTime}
                  onChange={(e) => setProductForm({...productForm, estimatedTime: parseInt(e.target.value) || 0})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="product-cost">التكلفة المقدرة (ريال)</Label>
                <Input
                  id="product-cost"
                  type="number"
                  step="0.01"
                  value={productForm.cost}
                  onChange={(e) => setProductForm({...productForm, cost: parseFloat(e.target.value) || 0})}
                />
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsProductDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleAddProduct}>
              إضافة المنتج
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog إضافة جزء */}
      <Dialog open={isPartDialogOpen} onOpenChange={setIsPartDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>إضافة جزء جديد</DialogTitle>
            <DialogDescription>
              أدخل بيانات الجزء الجديد للمنتج: {selectedProduct?.name}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="part-name">اسم الجزء *</Label>
                <Input
                  id="part-name"
                  value={partForm.name}
                  onChange={(e) => setPartForm({...partForm, name: e.target.value})}
                  placeholder="أدخل اسم الجزء"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="part-code">كود الجزء *</Label>
                <Input
                  id="part-code"
                  value={partForm.code}
                  onChange={(e) => setPartForm({...partForm, code: e.target.value})}
                  placeholder="أدخل كود الجزء"
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="part-description">الوصف</Label>
              <Textarea
                id="part-description"
                value={partForm.description}
                onChange={(e) => setPartForm({...partForm, description: e.target.value})}
                placeholder="أدخل وصف الجزء"
              />
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="part-quantity">الكمية</Label>
                <Input
                  id="part-quantity"
                  type="number"
                  value={partForm.quantity}
                  onChange={(e) => setPartForm({...partForm, quantity: parseInt(e.target.value) || 1})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="part-unit">الوحدة</Label>
                <Select value={partForm.unit} onValueChange={(value) => setPartForm({...partForm, unit: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="قطعة">قطعة</SelectItem>
                    <SelectItem value="كيلو">كيلو</SelectItem>
                    <SelectItem value="متر">متر</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="part-weight">الوزن (كيلو)</Label>
                <Input
                  id="part-weight"
                  type="number"
                  step="0.01"
                  value={partForm.weight}
                  onChange={(e) => setPartForm({...partForm, weight: parseFloat(e.target.value) || 0})}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="part-material">المادة</Label>
                <Input
                  id="part-material"
                  value={partForm.material}
                  onChange={(e) => setPartForm({...partForm, material: e.target.value})}
                  placeholder="نوع المادة الخام"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="part-dimensions">الأبعاد</Label>
                <Input
                  id="part-dimensions"
                  value={partForm.dimensions}
                  onChange={(e) => setPartForm({...partForm, dimensions: e.target.value})}
                  placeholder="الطول × العرض × الارتفاع"
                />
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPartDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleAddPart}>
              إضافة الجزء
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog إضافة عملية */}
      <Dialog open={isOperationDialogOpen} onOpenChange={setIsOperationDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>إضافة عملية جديدة</DialogTitle>
            <DialogDescription>
              أدخل بيانات العملية الجديدة للجزء: {selectedPart?.name}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4 max-h-[400px] overflow-y-auto">
            <div className="grid grid-cols-3 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="op-name">اسم العملية *</Label>
                <Input
                  id="op-name"
                  value={operationForm.name}
                  onChange={(e) => setOperationForm({...operationForm, name: e.target.value})}
                  placeholder="أدخل اسم العملية"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="op-code">كود العملية</Label>
                <Input
                  id="op-code"
                  value={operationForm.code}
                  onChange={(e) => setOperationForm({...operationForm, code: e.target.value})}
                  placeholder="أدخل كود العملية"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="op-sequence">الترتيب</Label>
                <Input
                  id="op-sequence"
                  type="number"
                  value={operationForm.sequence}
                  onChange={(e) => setOperationForm({...operationForm, sequence: parseInt(e.target.value) || 1})}
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="op-description">الوصف</Label>
              <Textarea
                id="op-description"
                value={operationForm.description}
                onChange={(e) => setOperationForm({...operationForm, description: e.target.value})}
                placeholder="أدخل وصف العملية"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="op-workshop">الورشة *</Label>
                <Select value={operationForm.workshopId} onValueChange={(value) => setOperationForm({...operationForm, workshopId: value, machineId: ''})}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الورشة" />
                  </SelectTrigger>
                  <SelectContent>
                    {workshops.map((workshop) => (
                      <SelectItem key={workshop.id} value={workshop.id}>
                        {workshop.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="op-machine">المكنة</Label>
                <Select value={operationForm.machineId} onValueChange={(value) => setOperationForm({...operationForm, machineId: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المكنة (اختياري)" />
                  </SelectTrigger>
                  <SelectContent>
                    {machines
                      .filter(m => m.workshopId === operationForm.workshopId)
                      .map((machine) => (
                      <SelectItem key={machine.id} value={machine.id}>
                        {machine.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="op-time">الوقت المقدر (دقيقة)</Label>
                <Input
                  id="op-time"
                  type="number"
                  value={operationForm.estimatedTime}
                  onChange={(e) => setOperationForm({...operationForm, estimatedTime: parseInt(e.target.value) || 0})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="op-setup">وقت التجهيز (دقيقة)</Label>
                <Input
                  id="op-setup"
                  type="number"
                  value={operationForm.setupTime}
                  onChange={(e) => setOperationForm({...operationForm, setupTime: parseInt(e.target.value) || 0})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="op-skill">مستوى المهارة</Label>
                <Select value={operationForm.skillLevel} onValueChange={(value: any) => setOperationForm({...operationForm, skillLevel: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">مبتدئ</SelectItem>
                    <SelectItem value="intermediate">متوسط</SelectItem>
                    <SelectItem value="advanced">متقدم</SelectItem>
                    <SelectItem value="expert">خبير</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="op-instructions">تعليمات العملية</Label>
              <Textarea
                id="op-instructions"
                value={operationForm.instructions}
                onChange={(e) => setOperationForm({...operationForm, instructions: e.target.value})}
                placeholder="أدخل تعليمات تنفيذ العملية"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOperationDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleAddOperation}>
              إضافة العملية
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
