import { Permission, Role, UserPermissions, DEFAULT_PERMISSIONS, DEFAULT_ROLES } from '@/types/permissions';

export class PermissionService {
  private static instance: PermissionService;
  private permissions: Permission[] = [];
  private roles: Role[] = [];
  private userPermissions: Map<string, UserPermissions> = new Map();

  private constructor() {
    this.initializePermissions();
  }

  public static getInstance(): PermissionService {
    if (!PermissionService.instance) {
      PermissionService.instance = new PermissionService();
    }
    return PermissionService.instance;
  }

  private initializePermissions(): void {
    // تحميل الأذونات من localStorage أو استخدام الافتراضية
    const storedPermissions = localStorage.getItem('system_permissions');
    const storedRoles = localStorage.getItem('system_roles');
    const storedUserPermissions = localStorage.getItem('user_permissions');

    this.permissions = storedPermissions ? JSON.parse(storedPermissions) : DEFAULT_PERMISSIONS;
    this.roles = storedRoles ? JSON.parse(storedRoles) : DEFAULT_ROLES;
    
    if (storedUserPermissions) {
      const userPermsArray = JSON.parse(storedUserPermissions);
      this.userPermissions = new Map(userPermsArray.map((up: UserPermissions) => [up.userId, up]));
    }

    // حفظ البيانات الافتراضية إذا لم تكن موجودة
    if (!storedPermissions) {
      this.savePermissions();
    }
    if (!storedRoles) {
      this.saveRoles();
    }
  }

  private savePermissions(): void {
    localStorage.setItem('system_permissions', JSON.stringify(this.permissions));
  }

  private saveRoles(): void {
    localStorage.setItem('system_roles', JSON.stringify(this.roles));
  }

  private saveUserPermissions(): void {
    const userPermsArray = Array.from(this.userPermissions.entries()).map(([userId, perms]) => perms);
    localStorage.setItem('user_permissions', JSON.stringify(userPermsArray));
  }

  // إدارة الأذونات
  public getAllPermissions(): Permission[] {
    return [...this.permissions];
  }

  public getPermissionsByModule(module: string): Permission[] {
    return this.permissions.filter(p => p.module === module);
  }

  public getPermissionById(id: string): Permission | undefined {
    return this.permissions.find(p => p.id === id);
  }

  public addPermission(permission: Permission): boolean {
    if (this.permissions.some(p => p.id === permission.id)) {
      return false; // الإذن موجود بالفعل
    }
    this.permissions.push(permission);
    this.savePermissions();
    return true;
  }

  public updatePermission(id: string, updates: Partial<Permission>): boolean {
    const index = this.permissions.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.permissions[index] = { ...this.permissions[index], ...updates };
    this.savePermissions();
    return true;
  }

  public deletePermission(id: string): boolean {
    const index = this.permissions.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.permissions.splice(index, 1);
    this.savePermissions();
    
    // إزالة الإذن من جميع الأدوار
    this.roles.forEach(role => {
      role.permissions = role.permissions.filter(pId => pId !== id);
    });
    this.saveRoles();
    
    return true;
  }

  // إدارة الأدوار
  public getAllRoles(): Role[] {
    return [...this.roles];
  }

  public getRoleById(id: string): Role | undefined {
    return this.roles.find(r => r.id === id);
  }

  public getRoleByName(name: string): Role | undefined {
    return this.roles.find(r => r.name === name);
  }

  public addRole(role: Role): boolean {
    if (this.roles.some(r => r.id === role.id || r.name === role.name)) {
      return false; // الدور موجود بالفعل
    }
    this.roles.push(role);
    this.saveRoles();
    return true;
  }

  public updateRole(id: string, updates: Partial<Role>): boolean {
    const index = this.roles.findIndex(r => r.id === id);
    if (index === -1) return false;

    // منع تعديل الأدوار النظامية
    if (this.roles[index].isSystemRole && updates.permissions) {
      return false;
    }

    this.roles[index] = { 
      ...this.roles[index], 
      ...updates, 
      updatedAt: new Date().toISOString() 
    };
    this.saveRoles();
    return true;
  }

  public deleteRole(id: string): boolean {
    const role = this.getRoleById(id);
    if (!role || role.isSystemRole) {
      return false; // لا يمكن حذف الأدوار النظامية
    }

    const index = this.roles.findIndex(r => r.id === id);
    if (index === -1) return false;

    this.roles.splice(index, 1);
    this.saveRoles();
    
    // إزالة الدور من جميع المستخدمين
    this.userPermissions.forEach((userPerm, userId) => {
      if (userPerm.roleId === id) {
        this.userPermissions.delete(userId);
      }
    });
    this.saveUserPermissions();
    
    return true;
  }

  // إدارة صلاحيات المستخدمين
  public getUserPermissions(userId: string): UserPermissions | undefined {
    return this.userPermissions.get(userId);
  }

  public setUserRole(userId: string, roleId: string, expiresAt?: string): boolean {
    const role = this.getRoleById(roleId);
    if (!role) {
      console.log(`Role not found: ${roleId}`);
      return false;
    }

    const userPerm: UserPermissions = {
      userId,
      roleId,
      customPermissions: [],
      deniedPermissions: [],
      expiresAt
    };

    this.userPermissions.set(userId, userPerm);
    this.saveUserPermissions();
    console.log(`User role set successfully: ${userId} -> ${roleId}`);
    console.log(`User permissions:`, this.userPermissions.get(userId));
    return true;
  }

  public addCustomPermission(userId: string, permissionId: string): boolean {
    const userPerm = this.userPermissions.get(userId);
    if (!userPerm) return false;

    if (!userPerm.customPermissions.includes(permissionId)) {
      userPerm.customPermissions.push(permissionId);
      this.saveUserPermissions();
    }
    return true;
  }

  public removeCustomPermission(userId: string, permissionId: string): boolean {
    const userPerm = this.userPermissions.get(userId);
    if (!userPerm) return false;

    userPerm.customPermissions = userPerm.customPermissions.filter(id => id !== permissionId);
    this.saveUserPermissions();
    return true;
  }

  public denyPermission(userId: string, permissionId: string): boolean {
    const userPerm = this.userPermissions.get(userId);
    if (!userPerm) return false;

    if (!userPerm.deniedPermissions.includes(permissionId)) {
      userPerm.deniedPermissions.push(permissionId);
      this.saveUserPermissions();
    }
    return true;
  }

  public allowPermission(userId: string, permissionId: string): boolean {
    const userPerm = this.userPermissions.get(userId);
    if (!userPerm) return false;

    userPerm.deniedPermissions = userPerm.deniedPermissions.filter(id => id !== permissionId);
    this.saveUserPermissions();
    return true;
  }

  // التحقق من الصلاحيات
  public hasPermission(userId: string, permissionId: string): boolean {
    const userPerm = this.userPermissions.get(userId);
    if (!userPerm) {
      console.log(`No permissions found for user: ${userId}`);
      return false;
    }

    // التحقق من انتهاء الصلاحية
    if (userPerm.expiresAt && new Date(userPerm.expiresAt) < new Date()) {
      console.log(`Permissions expired for user: ${userId}`);
      return false;
    }

    // التحقق من الأذونات المرفوضة
    if (userPerm.deniedPermissions.includes(permissionId)) {
      console.log(`Permission ${permissionId} denied for user: ${userId}`);
      return false;
    }

    // التحقق من الأذونات المخصصة
    if (userPerm.customPermissions.includes(permissionId)) {
      console.log(`Custom permission ${permissionId} granted for user: ${userId}`);
      return true;
    }

    // التحقق من أذونات الدور
    const role = this.getRoleById(userPerm.roleId);
    const hasRolePermission = role ? role.permissions.includes(permissionId) : false;
    console.log(`Role permission check for user ${userId}, role ${userPerm.roleId}, permission ${permissionId}: ${hasRolePermission}`);
    return hasRolePermission;
  }

  public hasAnyPermission(userId: string, permissionIds: string[]): boolean {
    return permissionIds.some(id => this.hasPermission(userId, id));
  }

  public hasAllPermissions(userId: string, permissionIds: string[]): boolean {
    return permissionIds.every(id => this.hasPermission(userId, id));
  }

  public getUserEffectivePermissions(userId: string): string[] {
    const userPerm = this.userPermissions.get(userId);
    if (!userPerm) return [];

    const role = this.getRoleById(userPerm.roleId);
    const rolePermissions = role ? role.permissions : [];
    
    // دمج أذونات الدور مع الأذونات المخصصة
    const allPermissions = [...new Set([...rolePermissions, ...userPerm.customPermissions])];
    
    // إزالة الأذونات المرفوضة
    return allPermissions.filter(id => !userPerm.deniedPermissions.includes(id));
  }

  // أدوات مساعدة
  public getModules(): string[] {
    return [...new Set(this.permissions.map(p => p.module))];
  }

  public getActions(): string[] {
    return [...new Set(this.permissions.map(p => p.action))];
  }

  public getResources(): string[] {
    return [...new Set(this.permissions.map(p => p.resource))];
  }

  public searchPermissions(query: string): Permission[] {
    const lowerQuery = query.toLowerCase();
    return this.permissions.filter(p => 
      p.name.toLowerCase().includes(lowerQuery) ||
      p.description.toLowerCase().includes(lowerQuery) ||
      p.module.toLowerCase().includes(lowerQuery)
    );
  }

  public getRolePermissions(roleId: string): Permission[] {
    const role = this.getRoleById(roleId);
    if (!role) return [];
    
    return this.permissions.filter(p => role.permissions.includes(p.id));
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const permissionService = PermissionService.getInstance();
