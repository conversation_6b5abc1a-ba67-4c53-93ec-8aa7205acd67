export interface SecuritySettings {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    maxAge: number; // days
    preventReuse: number; // number of previous passwords to remember
  };
  sessionPolicy: {
    maxDuration: number; // minutes
    idleTimeout: number; // minutes
    maxConcurrentSessions: number;
  };
  loginPolicy: {
    maxFailedAttempts: number;
    lockoutDuration: number; // minutes
    enableCaptcha: boolean;
    enableTwoFactor: boolean;
  };
  auditPolicy: {
    retentionDays: number;
    logFailedLogins: boolean;
    logSuccessfulLogins: boolean;
    logPermissionChanges: boolean;
    logDataAccess: boolean;
  };
}

export interface SecurityAlert {
  id: string;
  type: 'failed_login' | 'account_locked' | 'permission_escalation' | 'suspicious_activity' | 'password_expired';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  userName?: string;
  message: string;
  details: Record<string, any>;
  timestamp: string;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
}

export class SecurityService {
  private static instance: SecurityService;
  private settings: SecuritySettings;
  private alerts: SecurityAlert[] = [];
  private readonly STORAGE_KEY = 'security_settings';
  private readonly ALERTS_KEY = 'security_alerts';

  private constructor() {
    this.loadSettings();
    this.loadAlerts();
  }

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  private loadSettings(): void {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    this.settings = stored ? JSON.parse(stored) : this.getDefaultSettings();
  }

  private loadAlerts(): void {
    const stored = localStorage.getItem(this.ALERTS_KEY);
    this.alerts = stored ? JSON.parse(stored) : [];
  }

  private saveSettings(): void {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings));
  }

  private saveAlerts(): void {
    localStorage.setItem(this.ALERTS_KEY, JSON.stringify(this.alerts));
  }

  private getDefaultSettings(): SecuritySettings {
    return {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: false,
        maxAge: 90,
        preventReuse: 5
      },
      sessionPolicy: {
        maxDuration: 480, // 8 hours
        idleTimeout: 30,
        maxConcurrentSessions: 3
      },
      loginPolicy: {
        maxFailedAttempts: 5,
        lockoutDuration: 30,
        enableCaptcha: false,
        enableTwoFactor: false
      },
      auditPolicy: {
        retentionDays: 365,
        logFailedLogins: true,
        logSuccessfulLogins: true,
        logPermissionChanges: true,
        logDataAccess: false
      }
    };
  }

  // إدارة الإعدادات
  public getSettings(): SecuritySettings {
    return { ...this.settings };
  }

  public updateSettings(newSettings: Partial<SecuritySettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  public resetToDefaults(): void {
    this.settings = this.getDefaultSettings();
    this.saveSettings();
  }

  // التحقق من كلمة المرور
  public validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const policy = this.settings.passwordPolicy;

    if (password.length < policy.minLength) {
      errors.push(`كلمة المرور يجب أن تكون ${policy.minLength} أحرف على الأقل`);
    }

    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
    }

    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
    }

    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
    }

    if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  public getPasswordStrength(password: string): { score: number; label: string; color: string } {
    let score = 0;
    
    // الطول
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;

    // التنوع
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/\d/.test(password)) score += 1;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;

    // التعقيد
    if (password.length >= 10 && /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) score += 1;

    const labels = ['ضعيف جداً', 'ضعيف', 'متوسط', 'قوي', 'قوي جداً'];
    const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#16a34a'];
    
    const index = Math.min(Math.floor(score / 2), 4);
    
    return {
      score: (score / 8) * 100,
      label: labels[index],
      color: colors[index]
    };
  }

  // إدارة التنبيهات الأمنية
  public createAlert(
    type: SecurityAlert['type'],
    severity: SecurityAlert['severity'],
    message: string,
    details: Record<string, any> = {},
    userId?: string,
    userName?: string
  ): void {
    const alert: SecurityAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      userId,
      userName,
      message,
      details,
      timestamp: new Date().toISOString(),
      acknowledged: false
    };

    this.alerts.unshift(alert);
    
    // الاحتفاظ بآخر 1000 تنبيه فقط
    if (this.alerts.length > 1000) {
      this.alerts = this.alerts.slice(0, 1000);
    }
    
    this.saveAlerts();
  }

  public getAlerts(filter?: {
    type?: SecurityAlert['type'];
    severity?: SecurityAlert['severity'];
    acknowledged?: boolean;
    limit?: number;
  }): SecurityAlert[] {
    let filtered = [...this.alerts];

    if (filter) {
      if (filter.type) {
        filtered = filtered.filter(alert => alert.type === filter.type);
      }
      if (filter.severity) {
        filtered = filtered.filter(alert => alert.severity === filter.severity);
      }
      if (filter.acknowledged !== undefined) {
        filtered = filtered.filter(alert => alert.acknowledged === filter.acknowledged);
      }
      if (filter.limit) {
        filtered = filtered.slice(0, filter.limit);
      }
    }

    return filtered;
  }

  public acknowledgeAlert(alertId: string, acknowledgedBy: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (!alert) return false;

    alert.acknowledged = true;
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = new Date().toISOString();
    
    this.saveAlerts();
    return true;
  }

  public getAlertStats(): {
    total: number;
    unacknowledged: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    byType: Record<string, number>;
  } {
    const stats = {
      total: this.alerts.length,
      unacknowledged: 0,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      byType: {} as Record<string, number>
    };

    this.alerts.forEach(alert => {
      if (!alert.acknowledged) stats.unacknowledged++;
      
      switch (alert.severity) {
        case 'critical': stats.critical++; break;
        case 'high': stats.high++; break;
        case 'medium': stats.medium++; break;
        case 'low': stats.low++; break;
      }

      stats.byType[alert.type] = (stats.byType[alert.type] || 0) + 1;
    });

    return stats;
  }

  // فحص الأمان
  public performSecurityCheck(): {
    score: number;
    issues: Array<{ severity: string; message: string; recommendation: string }>;
  } {
    const issues: Array<{ severity: string; message: string; recommendation: string }> = [];
    let score = 100;

    // فحص سياسة كلمات المرور
    if (this.settings.passwordPolicy.minLength < 8) {
      issues.push({
        severity: 'high',
        message: 'الحد الأدنى لطول كلمة المرور أقل من 8 أحرف',
        recommendation: 'زيادة الحد الأدنى إلى 8 أحرف على الأقل'
      });
      score -= 15;
    }

    if (!this.settings.passwordPolicy.requireUppercase || !this.settings.passwordPolicy.requireLowercase) {
      issues.push({
        severity: 'medium',
        message: 'لا يتم طلب أحرف كبيرة وصغيرة في كلمة المرور',
        recommendation: 'تفعيل متطلب الأحرف الكبيرة والصغيرة'
      });
      score -= 10;
    }

    if (!this.settings.passwordPolicy.requireNumbers) {
      issues.push({
        severity: 'medium',
        message: 'لا يتم طلب أرقام في كلمة المرور',
        recommendation: 'تفعيل متطلب الأرقام'
      });
      score -= 10;
    }

    // فحص سياسة تسجيل الدخول
    if (this.settings.loginPolicy.maxFailedAttempts > 10) {
      issues.push({
        severity: 'medium',
        message: 'عدد محاولات تسجيل الدخول الفاشلة المسموحة مرتفع',
        recommendation: 'تقليل العدد إلى 5 محاولات أو أقل'
      });
      score -= 10;
    }

    // فحص سياسة الجلسات
    if (this.settings.sessionPolicy.maxDuration > 720) { // 12 hours
      issues.push({
        severity: 'low',
        message: 'مدة الجلسة القصوى طويلة جداً',
        recommendation: 'تقليل مدة الجلسة إلى 8 ساعات أو أقل'
      });
      score -= 5;
    }

    if (this.settings.sessionPolicy.idleTimeout > 60) {
      issues.push({
        severity: 'low',
        message: 'مهلة عدم النشاط طويلة',
        recommendation: 'تقليل مهلة عدم النشاط إلى 30 دقيقة أو أقل'
      });
      score -= 5;
    }

    // فحص التنبيهات غير المؤكدة
    const unacknowledgedAlerts = this.getAlerts({ acknowledged: false });
    if (unacknowledgedAlerts.length > 10) {
      issues.push({
        severity: 'medium',
        message: `يوجد ${unacknowledgedAlerts.length} تنبيه أمني غير مؤكد`,
        recommendation: 'مراجعة وتأكيد التنبيهات الأمنية'
      });
      score -= 15;
    }

    return {
      score: Math.max(0, score),
      issues
    };
  }

  // تنظيف البيانات القديمة
  public cleanupOldData(): { alertsRemoved: number } {
    const retentionDate = new Date();
    retentionDate.setDate(retentionDate.getDate() - this.settings.auditPolicy.retentionDays);
    
    const initialCount = this.alerts.length;
    this.alerts = this.alerts.filter(alert => new Date(alert.timestamp) >= retentionDate);
    const alertsRemoved = initialCount - this.alerts.length;
    
    if (alertsRemoved > 0) {
      this.saveAlerts();
    }
    
    return { alertsRemoved };
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const securityService = SecurityService.getInstance();
