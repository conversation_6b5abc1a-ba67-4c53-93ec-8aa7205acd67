// نظام الصلاحيات المتقدم
export interface Permission {
  id: string;
  name: string;
  description: string;
  module: string;
  action: string;
  resource: string;
}

export interface Role {
  id: string;
  name: string;
  displayName: string;
  description: string;
  permissions: string[];
  isSystemRole: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserPermissions {
  userId: string;
  roleId: string;
  customPermissions: string[];
  deniedPermissions: string[];
  expiresAt?: string;
}

// تعريف الوحدات والأذونات
export const MODULES = {
  DASHBOARD: 'dashboard',
  PLANNING: 'planning',
  PRODUCTION: 'production',
  QUALITY: 'quality',
  WAREHOUSE: 'warehouse',
  REPORTS: 'reports',
  USERS: 'users',
  SYSTEM: 'system'
} as const;

export const ACTIONS = {
  VIEW: 'view',
  CREATE: 'create',
  EDIT: 'edit',
  DELETE: 'delete',
  APPROVE: 'approve',
  EXPORT: 'export',
  IMPORT: 'import',
  MANAGE: 'manage'
} as const;

export const RESOURCES = {
  // Dashboard
  DASHBOARD_OVERVIEW: 'dashboard.overview',
  DASHBOARD_ANALYTICS: 'dashboard.analytics',
  
  // Planning
  WORK_ORDERS: 'planning.work_orders',
  PRODUCTION_SCHEDULE: 'planning.schedule',
  RESOURCE_ALLOCATION: 'planning.resources',
  
  // Production
  PRODUCTION_LINES: 'production.lines',
  PRODUCTION_DATA: 'production.data',
  MACHINE_STATUS: 'production.machines',
  
  // Quality
  QUALITY_CHECKS: 'quality.checks',
  QUALITY_REPORTS: 'quality.reports',
  DEFECT_TRACKING: 'quality.defects',
  
  // Warehouse
  INVENTORY: 'warehouse.inventory',
  STOCK_MOVEMENTS: 'warehouse.movements',
  SUPPLIERS: 'warehouse.suppliers',
  
  // Reports
  PRODUCTION_REPORTS: 'reports.production',
  QUALITY_REPORTS: 'reports.quality',
  INVENTORY_REPORTS: 'reports.inventory',
  CUSTOM_REPORTS: 'reports.custom',
  
  // Users & System
  USER_MANAGEMENT: 'users.management',
  ROLE_MANAGEMENT: 'users.roles',
  SYSTEM_SETTINGS: 'system.settings',
  AUDIT_LOGS: 'system.audit'
} as const;

// الأذونات الافتراضية
export const DEFAULT_PERMISSIONS: Permission[] = [
  // Dashboard Permissions
  {
    id: 'dashboard.view.overview',
    name: 'عرض لوحة التحكم',
    description: 'عرض لوحة التحكم الرئيسية والإحصائيات العامة',
    module: MODULES.DASHBOARD,
    action: ACTIONS.VIEW,
    resource: RESOURCES.DASHBOARD_OVERVIEW
  },
  {
    id: 'dashboard.view.analytics',
    name: 'عرض التحليلات',
    description: 'عرض التحليلات المتقدمة والرسوم البيانية',
    module: MODULES.DASHBOARD,
    action: ACTIONS.VIEW,
    resource: RESOURCES.DASHBOARD_ANALYTICS
  },

  // Planning Permissions
  {
    id: 'planning.view.work_orders',
    name: 'عرض أوامر العمل',
    description: 'عرض قائمة أوامر العمل والتفاصيل',
    module: MODULES.PLANNING,
    action: ACTIONS.VIEW,
    resource: RESOURCES.WORK_ORDERS
  },
  {
    id: 'planning.create.work_orders',
    name: 'إنشاء أوامر العمل',
    description: 'إنشاء أوامر عمل جديدة',
    module: MODULES.PLANNING,
    action: ACTIONS.CREATE,
    resource: RESOURCES.WORK_ORDERS
  },
  {
    id: 'planning.edit.work_orders',
    name: 'تعديل أوامر العمل',
    description: 'تعديل أوامر العمل الموجودة',
    module: MODULES.PLANNING,
    action: ACTIONS.EDIT,
    resource: RESOURCES.WORK_ORDERS
  },
  {
    id: 'planning.delete.work_orders',
    name: 'حذف أوامر العمل',
    description: 'حذف أوامر العمل',
    module: MODULES.PLANNING,
    action: ACTIONS.DELETE,
    resource: RESOURCES.WORK_ORDERS
  },
  {
    id: 'planning.manage.schedule',
    name: 'إدارة الجدولة',
    description: 'إدارة جدولة الإنتاج والموارد',
    module: MODULES.PLANNING,
    action: ACTIONS.MANAGE,
    resource: RESOURCES.PRODUCTION_SCHEDULE
  },

  // Production Permissions
  {
    id: 'production.view.lines',
    name: 'عرض خطوط الإنتاج',
    description: 'عرض حالة خطوط الإنتاج',
    module: MODULES.PRODUCTION,
    action: ACTIONS.VIEW,
    resource: RESOURCES.PRODUCTION_LINES
  },
  {
    id: 'production.edit.data',
    name: 'تعديل بيانات الإنتاج',
    description: 'تعديل وإدخال بيانات الإنتاج',
    module: MODULES.PRODUCTION,
    action: ACTIONS.EDIT,
    resource: RESOURCES.PRODUCTION_DATA
  },
  {
    id: 'production.view.machines',
    name: 'عرض حالة الآلات',
    description: 'عرض حالة وإحصائيات الآلات',
    module: MODULES.PRODUCTION,
    action: ACTIONS.VIEW,
    resource: RESOURCES.MACHINE_STATUS
  },

  // Quality Permissions
  {
    id: 'quality.view.checks',
    name: 'عرض فحوصات الجودة',
    description: 'عرض نتائج فحوصات الجودة',
    module: MODULES.QUALITY,
    action: ACTIONS.VIEW,
    resource: RESOURCES.QUALITY_CHECKS
  },
  {
    id: 'quality.create.checks',
    name: 'إنشاء فحوصات الجودة',
    description: 'إنشاء فحوصات جودة جديدة',
    module: MODULES.QUALITY,
    action: ACTIONS.CREATE,
    resource: RESOURCES.QUALITY_CHECKS
  },
  {
    id: 'quality.manage.defects',
    name: 'إدارة العيوب',
    description: 'إدارة وتتبع العيوب والمشاكل',
    module: MODULES.QUALITY,
    action: ACTIONS.MANAGE,
    resource: RESOURCES.DEFECT_TRACKING
  },

  // Warehouse Permissions
  {
    id: 'warehouse.view.inventory',
    name: 'عرض المخزون',
    description: 'عرض حالة المخزون والمواد',
    module: MODULES.WAREHOUSE,
    action: ACTIONS.VIEW,
    resource: RESOURCES.INVENTORY
  },
  {
    id: 'warehouse.edit.inventory',
    name: 'تعديل المخزون',
    description: 'تعديل كميات وبيانات المخزون',
    module: MODULES.WAREHOUSE,
    action: ACTIONS.EDIT,
    resource: RESOURCES.INVENTORY
  },
  {
    id: 'warehouse.view.movements',
    name: 'عرض حركات المخزون',
    description: 'عرض حركات دخول وخروج المخزون',
    module: MODULES.WAREHOUSE,
    action: ACTIONS.VIEW,
    resource: RESOURCES.STOCK_MOVEMENTS
  },

  // Reports Permissions
  {
    id: 'reports.view.production',
    name: 'عرض تقارير الإنتاج',
    description: 'عرض تقارير الإنتاج والأداء',
    module: MODULES.REPORTS,
    action: ACTIONS.VIEW,
    resource: RESOURCES.PRODUCTION_REPORTS
  },
  {
    id: 'reports.export.all',
    name: 'تصدير التقارير',
    description: 'تصدير التقارير بصيغ مختلفة',
    module: MODULES.REPORTS,
    action: ACTIONS.EXPORT,
    resource: RESOURCES.CUSTOM_REPORTS
  },

  // Admin Permissions
  {
    id: 'users.manage.all',
    name: 'إدارة المستخدمين',
    description: 'إدارة كاملة للمستخدمين والحسابات',
    module: MODULES.USERS,
    action: ACTIONS.MANAGE,
    resource: RESOURCES.USER_MANAGEMENT
  },
  {
    id: 'users.manage.roles',
    name: 'إدارة الأدوار',
    description: 'إدارة الأدوار والصلاحيات',
    module: MODULES.USERS,
    action: ACTIONS.MANAGE,
    resource: RESOURCES.ROLE_MANAGEMENT
  },
  {
    id: 'system.manage.settings',
    name: 'إدارة إعدادات النظام',
    description: 'إدارة إعدادات النظام العامة',
    module: MODULES.SYSTEM,
    action: ACTIONS.MANAGE,
    resource: RESOURCES.SYSTEM_SETTINGS
  },
  {
    id: 'system.view.audit',
    name: 'عرض سجل العمليات',
    description: 'عرض سجل العمليات والأنشطة',
    module: MODULES.SYSTEM,
    action: ACTIONS.VIEW,
    resource: RESOURCES.AUDIT_LOGS
  }
];

// الأدوار الافتراضية
export const DEFAULT_ROLES: Role[] = [
  {
    id: 'admin',
    name: 'admin',
    displayName: 'مدير النظام',
    description: 'صلاحيات كاملة لإدارة النظام والمستخدمين',
    permissions: DEFAULT_PERMISSIONS.map(p => p.id),
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'planning_manager',
    name: 'planning_manager',
    displayName: 'مدير التخطيط',
    description: 'إدارة كاملة لقسم التخطيط والجدولة',
    permissions: [
      'dashboard.view.overview',
      'dashboard.view.analytics',
      'planning.view.work_orders',
      'planning.create.work_orders',
      'planning.edit.work_orders',
      'planning.delete.work_orders',
      'planning.manage.schedule',
      'production.view.lines',
      'reports.view.production',
      'reports.export.all'
    ],
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'planning_user',
    name: 'planning_user',
    displayName: 'موظف التخطيط',
    description: 'صلاحيات أساسية لقسم التخطيط',
    permissions: [
      'dashboard.view.overview',
      'planning.view.work_orders',
      'planning.create.work_orders',
      'planning.edit.work_orders',
      'production.view.lines',
      'reports.view.production'
    ],
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'production_manager',
    name: 'production_manager',
    displayName: 'مدير الإنتاج',
    description: 'إدارة كاملة لقسم الإنتاج',
    permissions: [
      'dashboard.view.overview',
      'dashboard.view.analytics',
      'planning.view.work_orders',
      'production.view.lines',
      'production.edit.data',
      'production.view.machines',
      'quality.view.checks',
      'reports.view.production',
      'reports.export.all'
    ],
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'production_user',
    name: 'production_user',
    displayName: 'موظف الإنتاج',
    description: 'صلاحيات أساسية لقسم الإنتاج',
    permissions: [
      'dashboard.view.overview',
      'planning.view.work_orders',
      'production.view.lines',
      'production.edit.data',
      'production.view.machines'
    ],
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'quality_manager',
    name: 'quality_manager',
    displayName: 'مدير الجودة',
    description: 'إدارة كاملة لقسم الجودة',
    permissions: [
      'dashboard.view.overview',
      'dashboard.view.analytics',
      'production.view.lines',
      'quality.view.checks',
      'quality.create.checks',
      'quality.manage.defects',
      'reports.view.production',
      'reports.export.all'
    ],
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'quality_user',
    name: 'quality_user',
    displayName: 'موظف الجودة',
    description: 'صلاحيات أساسية لقسم الجودة',
    permissions: [
      'dashboard.view.overview',
      'production.view.lines',
      'quality.view.checks',
      'quality.create.checks'
    ],
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'warehouse_manager',
    name: 'warehouse_manager',
    displayName: 'مدير المخزن',
    description: 'إدارة كاملة للمخزن والمواد',
    permissions: [
      'dashboard.view.overview',
      'dashboard.view.analytics',
      'warehouse.view.inventory',
      'warehouse.edit.inventory',
      'warehouse.view.movements',
      'reports.view.production',
      'reports.export.all'
    ],
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'warehouse_user',
    name: 'warehouse_user',
    displayName: 'موظف المخزن',
    description: 'صلاحيات أساسية للمخزن',
    permissions: [
      'dashboard.view.overview',
      'warehouse.view.inventory',
      'warehouse.edit.inventory',
      'warehouse.view.movements'
    ],
    isSystemRole: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];
