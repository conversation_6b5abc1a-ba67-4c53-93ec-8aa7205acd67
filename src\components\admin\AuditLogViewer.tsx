import { useState, useMemo } from 'react';
import { AuditLog, AuditFilter } from '@/services/auditService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Search, 
  Filter, 
  Download, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock,
  User,
  Shield,
  Settings,
  Database,
  AlertTriangle
} from "lucide-react";

interface AuditLogViewerProps {
  logs: AuditLog[];
  onExport?: () => void;
}

const getActionIcon = (action: string) => {
  if (action.includes('login') || action.includes('logout')) return User;
  if (action.includes('user') || action.includes('role') || action.includes('permission')) return Shield;
  if (action.includes('system') || action.includes('settings')) return Settings;
  if (action.includes('data')) return Database;
  return AlertTriangle;
};

const getActionColor = (action: string) => {
  if (action.includes('login')) return 'text-green-600';
  if (action.includes('logout')) return 'text-gray-600';
  if (action.includes('delete')) return 'text-red-600';
  if (action.includes('create') || action.includes('add')) return 'text-blue-600';
  if (action.includes('update') || action.includes('edit')) return 'text-amber-600';
  return 'text-gray-600';
};

const formatAction = (action: string): string => {
  const actionMap: Record<string, string> = {
    'user.login': 'تسجيل دخول',
    'user.logout': 'تسجيل خروج',
    'user.create': 'إنشاء مستخدم',
    'user.update': 'تحديث مستخدم',
    'user.delete': 'حذف مستخدم',
    'user.activate': 'تفعيل مستخدم',
    'user.deactivate': 'إلغاء تفعيل مستخدم',
    'user.password_change': 'تغيير كلمة المرور',
    'user.password_reset': 'إعادة تعيين كلمة المرور',
    'role.create': 'إنشاء دور',
    'role.update': 'تحديث دور',
    'role.delete': 'حذف دور',
    'role.assign': 'تعيين دور',
    'role.revoke': 'إلغاء دور',
    'permission.grant': 'منح صلاحية',
    'permission.revoke': 'إلغاء صلاحية',
    'system.settings_update': 'تحديث إعدادات النظام'
  };
  
  return actionMap[action] || action;
};

export function AuditLogViewer({ logs, onExport }: AuditLogViewerProps) {
  const [filters, setFilters] = useState<AuditFilter>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  // تطبيق الفلاتر والبحث
  const filteredLogs = useMemo(() => {
    let filtered = logs;

    // تطبيق فلاتر التاريخ
    if (filters.startDate) {
      filtered = filtered.filter(log => log.timestamp >= filters.startDate!);
    }
    if (filters.endDate) {
      filtered = filtered.filter(log => log.timestamp <= filters.endDate!);
    }

    // تطبيق فلتر المستخدم
    if (filters.userId) {
      filtered = filtered.filter(log => log.userId === filters.userId);
    }

    // تطبيق فلتر العملية
    if (filters.action) {
      filtered = filtered.filter(log => log.action.includes(filters.action!));
    }

    // تطبيق فلتر النجاح/الفشل
    if (filters.success !== undefined) {
      filtered = filtered.filter(log => log.success === filters.success);
    }

    // تطبيق البحث النصي
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(log => 
        log.userName.toLowerCase().includes(term) ||
        log.action.toLowerCase().includes(term) ||
        log.resource.toLowerCase().includes(term) ||
        JSON.stringify(log.details).toLowerCase().includes(term)
      );
    }

    return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }, [logs, filters, searchTerm]);

  // تطبيق التصفح
  const paginatedLogs = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredLogs.slice(startIndex, startIndex + pageSize);
  }, [filteredLogs, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredLogs.length / pageSize);

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setCurrentPage(1);
  };

  const uniqueUsers = useMemo(() => 
    [...new Set(logs.map(log => ({ id: log.userId, name: log.userName })))]
      .filter((user, index, self) => self.findIndex(u => u.id === user.id) === index),
    [logs]
  );

  const uniqueActions = useMemo(() => 
    [...new Set(logs.map(log => log.action))],
    [logs]
  );

  return (
    <div className="space-y-6">
      {/* شريط البحث والفلاتر */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            سجل العمليات
          </CardTitle>
          <CardDescription>
            عرض وتصفية جميع العمليات المسجلة في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* البحث */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="البحث في السجلات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* الفلاتر */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="user-filter">المستخدم</Label>
                <Select value={filters.userId || ''} onValueChange={(value) => setFilters(prev => ({ ...prev, userId: value || undefined }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع المستخدمين" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع المستخدمين</SelectItem>
                    {uniqueUsers.map(user => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="action-filter">العملية</Label>
                <Select value={filters.action || ''} onValueChange={(value) => setFilters(prev => ({ ...prev, action: value || undefined }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع العمليات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع العمليات</SelectItem>
                    {uniqueActions.map(action => (
                      <SelectItem key={action} value={action}>
                        {formatAction(action)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="success-filter">النتيجة</Label>
                <Select value={filters.success?.toString() || ''} onValueChange={(value) => setFilters(prev => ({ ...prev, success: value ? value === 'true' : undefined }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع النتائج" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع النتائج</SelectItem>
                    <SelectItem value="true">نجح</SelectItem>
                    <SelectItem value="false">فشل</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="start-date">من تاريخ</Label>
                <Input
                  type="date"
                  value={filters.startDate?.split('T')[0] || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value ? `${e.target.value}T00:00:00.000Z` : undefined }))}
                />
              </div>

              <div>
                <Label htmlFor="end-date">إلى تاريخ</Label>
                <Input
                  type="date"
                  value={filters.endDate?.split('T')[0] || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value ? `${e.target.value}T23:59:59.999Z` : undefined }))}
                />
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex gap-2">
              <Button variant="outline" onClick={clearFilters}>
                <Filter className="h-4 w-4 mr-2" />
                مسح الفلاتر
              </Button>
              {onExport && (
                <Button variant="outline" onClick={onExport}>
                  <Download className="h-4 w-4 mr-2" />
                  تصدير
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* الجدول */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">#</TableHead>
                <TableHead>المستخدم</TableHead>
                <TableHead>العملية</TableHead>
                <TableHead>المورد</TableHead>
                <TableHead>النتيجة</TableHead>
                <TableHead>التاريخ</TableHead>
                <TableHead className="w-12">التفاصيل</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedLogs.map((log, index) => {
                const ActionIcon = getActionIcon(log.action);
                const actionColor = getActionColor(log.action);
                
                return (
                  <TableRow key={log.id}>
                    <TableCell>{(currentPage - 1) * pageSize + index + 1}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarFallback className="text-xs">
                            {log.userName.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{log.userName}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <ActionIcon className={`h-4 w-4 ${actionColor}`} />
                        <span className="text-sm">{formatAction(log.action)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {log.resource}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {log.success ? (
                        <Badge variant="default" className="flex items-center gap-1 w-fit">
                          <CheckCircle className="h-3 w-3" />
                          نجح
                        </Badge>
                      ) : (
                        <Badge variant="destructive" className="flex items-center gap-1 w-fit">
                          <XCircle className="h-3 w-3" />
                          فشل
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(log.timestamp).toLocaleDateString('ar-EG', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="ghost" size="sm" onClick={() => setSelectedLog(log)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[600px]">
                          <DialogHeader>
                            <DialogTitle>تفاصيل العملية</DialogTitle>
                            <DialogDescription>
                              معلومات مفصلة عن العملية المسجلة
                            </DialogDescription>
                          </DialogHeader>
                          {selectedLog && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label className="text-sm font-medium">المستخدم</Label>
                                  <p className="text-sm">{selectedLog.userName}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">العملية</Label>
                                  <p className="text-sm">{formatAction(selectedLog.action)}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">المورد</Label>
                                  <p className="text-sm">{selectedLog.resource}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">النتيجة</Label>
                                  <p className="text-sm">{selectedLog.success ? 'نجح' : 'فشل'}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">التاريخ</Label>
                                  <p className="text-sm">
                                    {new Date(selectedLog.timestamp).toLocaleString('ar-EG')}
                                  </p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">معرف العملية</Label>
                                  <p className="text-sm font-mono">{selectedLog.id}</p>
                                </div>
                              </div>
                              
                              {selectedLog.errorMessage && (
                                <div>
                                  <Label className="text-sm font-medium text-destructive">رسالة الخطأ</Label>
                                  <p className="text-sm text-destructive">{selectedLog.errorMessage}</p>
                                </div>
                              )}
                              
                              <div>
                                <Label className="text-sm font-medium">التفاصيل</Label>
                                <pre className="text-xs bg-muted p-3 rounded-md overflow-auto max-h-40">
                                  {JSON.stringify(selectedLog.details, null, 2)}
                                </pre>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          
          {paginatedLogs.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              لا توجد سجلات تطابق المعايير المحددة
            </div>
          )}
        </CardContent>
      </Card>

      {/* التصفح */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            عرض {(currentPage - 1) * pageSize + 1} إلى {Math.min(currentPage * pageSize, filteredLogs.length)} من {filteredLogs.length} سجل
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              السابق
            </Button>
            <span className="flex items-center px-3 text-sm">
              {currentPage} من {totalPages}
            </span>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              التالي
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
