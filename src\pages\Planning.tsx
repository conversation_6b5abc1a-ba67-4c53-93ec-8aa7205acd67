import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useWorkOrders } from "@/contexts/WorkOrderContext";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, 
  Calendar, 
  Package, 
  Edit, 
  Trash2,
  ClipboardList
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function Planning() {
  const { workOrders, addWorkOrder, getProductionByWorkOrder, getQualityByWorkOrder, parts, addPart, getPartsByWorkOrder } = useWorkOrders();
  const { toast } = useToast();
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newOrder, setNewOrder] = useState({
    orderNumber: "",
    productName: "",
    quantity: "",
    startDate: "",
    endDate: "",
  });

  const [isPartDialogOpen, setIsPartDialogOpen] = useState(false);
  const [newPart, setNewPart] = useState({
    workOrderId: "",
    partName: "",
    quantity: "",
  });

  const handleCreateOrder = () => {
    try {
      addWorkOrder({
        orderNumber: newOrder.orderNumber,
        productName: newOrder.productName,
        quantity: parseInt(newOrder.quantity),
        startDate: newOrder.startDate,
        endDate: newOrder.endDate,
        status: "planned",
        createdBy: "مستخدم التخطيط"
      });
      
      setNewOrder({
        orderNumber: "",
        productName: "",
        quantity: "",
        startDate: "",
        endDate: "",
      });
      setIsDialogOpen(false);
      
      toast({
        title: "تم إنشاء أمر الشغل بنجاح",
        description: `أمر الشغل ${newOrder.orderNumber} للمنتج ${newOrder.productName} تم إضافته`,
      });
    } catch (error) {
      toast({
        title: "خطأ في إنشاء أمر الشغل",
        description: "حدث خطأ أثناء إنشاء أمر الشغل، حاول مرة أخرى",
        variant: "destructive",
      });
    }
  };

  const handleCreatePart = () => {
    try {
      addPart({
        workOrderId: newPart.workOrderId,
        partName: newPart.partName,
        quantity: parseInt(newPart.quantity),
      });

      setNewPart({
        workOrderId: "",
        partName: "",
        quantity: "",
      });
      setIsPartDialogOpen(false);

      toast({
        title: "تم إضافة الجزء بنجاح",
        description: `تم إضافة الجزء ${newPart.partName}`,
      });
    } catch (error) {
      toast({
        title: "خطأ في إضافة الجزء",
        description: "حدث خطأ أثناء إضافة الجزء، حاول مرة أخرى",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "planned":
        return <Badge variant="outline">مخطط</Badge>;
      case "in_progress":
        return <Badge className="bg-warning text-warning-foreground">قيد التنفيذ</Badge>;
      case "completed":
        return <Badge className="bg-success text-success-foreground">مكتمل</Badge>;
      case "delayed":
        return <Badge variant="destructive">متأخر</Badge>;
      default:
        return <Badge variant="secondary">غير محدد</Badge>;
    }
  };

  return (
    <div className="space-y-6" dir="rtl">
      {/* العنوان والإجراءات */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center">
            <ClipboardList className="h-8 w-8 ml-3 text-primary" />
            قطاع التخطيط
          </h1>
          <p className="text-muted-foreground mt-2">إدارة أوامر الشغل ومتابعة خطط الإنتاج</p>
        </div>
        
        <div className="flex gap-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-primary hover:bg-primary-dark">
                <Plus className="h-4 w-4 ml-2" />
                أمر شغل جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إنشاء أمر شغل جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="orderNumber">رقم أمر الشغل</Label>
                  <Input
                    id="orderNumber"
                    value={newOrder.orderNumber}
                    onChange={(e) => setNewOrder({...newOrder, orderNumber: e.target.value})}
                    placeholder="أدخل رقم أمر الشغل"
                  />
                </div>
                
                <div>
                  <Label htmlFor="productName">اسم المنتج</Label>
                  <Input
                    id="productName"
                    value={newOrder.productName}
                    onChange={(e) => setNewOrder({...newOrder, productName: e.target.value})}
                    placeholder="أدخل اسم المنتج"
                  />
                </div>
                
                <div>
                  <Label htmlFor="quantity">الكمية المطلوبة</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={newOrder.quantity}
                    onChange={(e) => setNewOrder({...newOrder, quantity: e.target.value})}
                    placeholder="أدخل الكمية المطلوبة"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">تاريخ البداية</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={newOrder.startDate}
                      onChange={(e) => setNewOrder({...newOrder, startDate: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="endDate">تاريخ النهاية</Label>
                    <Input
                      id="endDate"
                      type="date"
                      value={newOrder.endDate}
                      onChange={(e) => setNewOrder({...newOrder, endDate: e.target.value})}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button 
                    onClick={handleCreateOrder}
                    className="flex-1 bg-primary hover:bg-primary-dark"
                    disabled={!newOrder.orderNumber || !newOrder.productName || !newOrder.quantity || !newOrder.startDate || !newOrder.endDate}
                  >
                    إنشاء الأمر
                  </Button>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isPartDialogOpen} onOpenChange={setIsPartDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-secondary hover:bg-secondary-dark">
                <Plus className="h-4 w-4 ml-2" />
                إضافة جزء جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة جزء جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="workOrderId">اختر أمر الشغل</Label>
                  <select
                    id="workOrderId"
                    value={newPart.workOrderId}
                    onChange={(e) => setNewPart({...newPart, workOrderId: e.target.value})}
                    className="w-full border rounded px-2 py-1"
                  >
                    <option value="">اختر أمر الشغل</option>
                    {workOrders.map(order => (
                      <option key={order.id} value={order.id}>
                        #{order.orderNumber} - {order.productName}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label htmlFor="partName">اسم الجزء</Label>
                  <Input
                    id="partName"
                    value={newPart.partName}
                    onChange={(e) => setNewPart({...newPart, partName: e.target.value})}
                    placeholder="أدخل اسم الجزء"
                  />
                </div>

                <div>
                  <Label htmlFor="quantity">الكمية</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={newPart.quantity}
                    onChange={(e) => setNewPart({...newPart, quantity: e.target.value})}
                    placeholder="أدخل الكمية"
                  />
                </div>

                <div className="flex gap-2 pt-4">
                  <Button 
                    onClick={handleCreatePart}
                    className="flex-1 bg-secondary hover:bg-secondary-dark"
                    disabled={!newPart.workOrderId || !newPart.partName || !newPart.quantity}
                  >
                    إضافة الجزء
                  </Button>
                  <Button variant="outline" onClick={() => setIsPartDialogOpen(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أوامر الشغل النشطة</CardTitle>
            <ClipboardList className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workOrders.filter(o => o.status === 'in_progress').length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الكميات</CardTitle>
            <Package className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workOrders.reduce((sum, order) => sum + order.quantity, 0).toLocaleString()}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المنتجات المختلفة</CardTitle>
            <Calendar className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(workOrders.map(o => o.productName)).size}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* قائمة أوامر الشغل */}
      <Card>
        <CardHeader>
          <CardTitle>أوامر الشغل</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {workOrders.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                لا توجد أوامر شغل حالياً. قم بإنشاء أمر شغل جديد للبدء.
              </div>
            ) : (
              workOrders.map((order) => {
                const productions = getProductionByWorkOrder(order.id);
                const qualities = getQualityByWorkOrder(order.id);
                const orderParts = getPartsByWorkOrder(order.id);
                
                const totalProduced = productions.reduce((sum, p) => sum + p.producedQuantity, 0);
                const totalDelivered = qualities.reduce((sum, q) => sum + q.deliveredToWarehouse, 0);
                const remaining = order.quantity - totalDelivered;
                
                return (
                  <div key={order.id} className="border rounded-lg p-4 hover:bg-accent/50 transition-colors">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-3">
                        <span className="font-bold text-primary">#{order.orderNumber}</span>
                        {getStatusBadge(order.status)}
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="text-destructive">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-muted-foreground">المنتج:</span>
                        <p className="font-semibold">{order.productName}</p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">الكمية المطلوبة:</span>
                        <p className="font-semibold">{order.quantity.toLocaleString()}</p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">تاريخ البداية:</span>
                        <p className="font-semibold">{new Date(order.startDate).toLocaleDateString('ar-EG')}</p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">تاريخ النهاية:</span>
                        <p className="font-semibold">{new Date(order.endDate).toLocaleDateString('ar-EG')}</p>
                      </div>
                    </div>
                    
                    <div className="mt-3 pt-3 border-t grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-muted-foreground">المُنتج:</span>
                        <p className="font-semibold text-blue-600">{totalProduced}</p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">المُسلم للمخزن:</span>
                        <p className="font-semibold text-green-600">{totalDelivered}</p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">المتبقي:</span>
                        <p className="font-semibold text-orange-600">{remaining}</p>
                      </div>
                    </div>

                    {/* عرض الأجزاء المرتبطة بأمر الشغل */}
                    <div className="mt-4">
                      <h3 className="text-lg font-semibold mb-2">الأجزاء المرتبطة</h3>
                      {orderParts.length === 0 ? (
                        <p className="text-muted-foreground">لا توجد أجزاء مضافة لهذا الأمر.</p>
                      ) : (
                        <ul className="list-disc list-inside space-y-1">
                          {orderParts.map(part => (
                            <li key={part.id}>
                              {part.partName} - الكمية: {part.quantity}
                            </li>
