import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { productionService } from '@/services/productionService';
import { Product, WorkOrder, Workshop, Machine } from '@/types/production';
import { 
  Plus, 
  Calendar, 
  Package, 
  Factory,
  Users,
  Clock,
  BarChart3,
  FileText,
  Settings,
  TrendingUp,
  Activity
} from "lucide-react";

export default function Planning() {
  const [products, setProducts] = useState<Product[]>([]);
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [workshops, setWorkshops] = useState<Workshop[]>([]);
  const [machines, setMachines] = useState<Machine[]>([]);
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalWorkOrders: 0,
    activeWorkOrders: 0,
    completedWorkOrders: 0,
    totalWorkshops: 0,
    totalMachines: 0,
    machineUtilization: 0,
    workshopEfficiency: 0
  });
  
  const { toast } = useToast();
  
  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const productsData = productionService.getProducts();
    const workOrdersData = productionService.getWorkOrders();
    const workshopsData = productionService.getWorkshops();
    const machinesData = productionService.getMachines();
    const statsData = productionService.getProductionStats();
    
    setProducts(productsData);
    setWorkOrders(workOrdersData);
    setWorkshops(workshopsData);
    setMachines(machinesData);
    setStats({
      totalProducts: statsData.totalProducts,
      totalWorkOrders: workOrdersData.length,
      activeWorkOrders: statsData.activeWorkOrders,
      completedWorkOrders: statsData.completedWorkOrders,
      totalWorkshops: statsData.totalWorkshops,
      totalMachines: statsData.totalMachines,
      machineUtilization: statsData.machineUtilization,
      workshopEfficiency: statsData.workshopEfficiency
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">في الانتظار</Badge>;
      case 'in_progress':
        return <Badge variant="default" className="bg-blue-600">قيد التنفيذ</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-600">مكتمل</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>;
      case 'on_hold':
        return <Badge variant="default" className="bg-yellow-600">معلق</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Badge variant="destructive">عاجل</Badge>;
      case 'high':
        return <Badge variant="default" className="bg-orange-600">عالي</Badge>;
      case 'medium':
        return <Badge variant="default" className="bg-blue-600">متوسط</Badge>;
      case 'low':
        return <Badge variant="secondary">منخفض</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'غير محدد';
  };

  const getAvailableMachines = () => {
    return machines.filter(m => m.status === 'available').length;
  };

  const getBusyMachines = () => {
    return machines.filter(m => m.status === 'busy').length;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">التخطيط الإنتاجي</h1>
          <p className="text-muted-foreground mt-2">
            مراقبة وتخطيط العمليات الإنتاجية
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => window.location.href = '/work-orders'}>
            <Plus className="ml-2 h-4 w-4" />
            أمر شغل جديد
          </Button>
          <Button variant="outline" onClick={() => window.location.href = '/products'}>
            <Package className="ml-2 h-4 w-4" />
            إدارة المنتجات
          </Button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المنتجات</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              منتج مسجل
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أوامر الشغل</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalWorkOrders}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeWorkOrders} نشط، {stats.completedWorkOrders} مكتمل
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الورش</CardTitle>
            <Factory className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalWorkshops}</div>
            <p className="text-xs text-muted-foreground">
              كفاءة {stats.workshopEfficiency.toFixed(1)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المكائن</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMachines}</div>
            <p className="text-xs text-muted-foreground">
              {getAvailableMachines()} متاحة، {getBusyMachines()} مشغولة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* أوامر الشغل النشطة */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>أوامر الشغل النشطة</CardTitle>
              <CardDescription>
                أوامر الشغل قيد التنفيذ أو في الانتظار
              </CardDescription>
            </div>
            <Button variant="outline" onClick={() => window.location.href = '/work-orders'}>
              عرض الكل
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الأمر</TableHead>
                <TableHead>المنتج</TableHead>
                <TableHead>العميل</TableHead>
                <TableHead>الكمية</TableHead>
                <TableHead>الأولوية</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>تاريخ التسليم</TableHead>
                <TableHead>التقدم</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {workOrders
                .filter(wo => wo.status === 'in_progress' || wo.status === 'pending')
                .slice(0, 10)
                .map((workOrder) => {
                  const completedBatches = workOrder.batches.filter(b => b.status === 'completed').length;
                  const totalBatches = workOrder.batches.length;
                  const progress = totalBatches > 0 ? (completedBatches / totalBatches) * 100 : 0;
                  
                  return (
                    <TableRow key={workOrder.id}>
                      <TableCell className="font-mono">{workOrder.orderNumber}</TableCell>
                      <TableCell className="font-medium">{getProductName(workOrder.productId)}</TableCell>
                      <TableCell>{workOrder.customerName}</TableCell>
                      <TableCell>{workOrder.quantity}</TableCell>
                      <TableCell>{getPriorityBadge(workOrder.priority)}</TableCell>
                      <TableCell>{getStatusBadge(workOrder.status)}</TableCell>
                      <TableCell>{new Date(workOrder.requestedDate).toLocaleDateString('ar-EG')}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {progress.toFixed(0)}%
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
          
          {workOrders.filter(wo => wo.status === 'in_progress' || wo.status === 'pending').length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد أوامر شغل نشطة حالياً</p>
              <Button className="mt-4" onClick={() => window.location.href = '/work-orders'}>
                <Plus className="ml-2 h-4 w-4" />
                إضافة أمر شغل جديد
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* حالة الورش */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {workshops.map((workshop) => {
          const workshopMachines = machines.filter(m => m.workshopId === workshop.id);
          const availableMachines = workshopMachines.filter(m => m.status === 'available').length;
          const busyMachines = workshopMachines.filter(m => m.status === 'busy').length;
          const maintenanceMachines = workshopMachines.filter(m => m.status === 'maintenance').length;
          
          return (
            <Card key={workshop.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Factory className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">{workshop.name}</CardTitle>
                  </div>
                  <Badge variant="outline">{workshop.code}</Badge>
                </div>
                <CardDescription>{workshop.location}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">المشرف:</span>
                    <span className="font-medium">{workshop.supervisor}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">العمال:</span>
                    <span className="font-medium">{workshop.currentLoad}/{workshop.capacity}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">المكائن:</span>
                    <div className="flex gap-1">
                      <Badge variant="default" className="bg-green-600 text-xs">
                        {availableMachines} متاحة
                      </Badge>
                      <Badge variant="default" className="bg-blue-600 text-xs">
                        {busyMachines} مشغولة
                      </Badge>
                      {maintenanceMachines > 0 && (
                        <Badge variant="default" className="bg-yellow-600 text-xs">
                          {maintenanceMachines} صيانة
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">ساعات العمل:</span>
                    <span className="font-medium">
                      {workshop.workingHours.start} - {workshop.workingHours.end}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {workshops.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Factory className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground mb-4">لا توجد ورش مسجلة</p>
            <Button onClick={() => window.location.href = '/workshops'}>
              <Plus className="ml-2 h-4 w-4" />
              إضافة ورشة جديدة
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
