import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/contexts/LocalAuthContext';
import { auditService, AuditLog } from '@/services/auditService';
import {
  Clock,
  Download,
  Trash2,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Activity,
  TrendingUp,
  Calendar
} from "lucide-react";

export default function AuditLogs() {
  const { currentUser, hasPermission } = useAuth();
  const { toast } = useToast();
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    successful: 0,
    failed: 0,
    todayCount: 0,
    weekCount: 0,
    monthCount: 0
  });

  useEffect(() => {
    loadLogs();
    loadStats();
  }, []);

  const loadLogs = () => {
    const allLogs = auditService.getLogs();
    setLogs(allLogs);
  };

  const loadStats = () => {
    const auditStats = auditService.getLogStats();
    setStats(auditStats);
  };

  const handleExport = () => {
    try {
      const exportData = auditService.exportLogs();
      const blob = new Blob([exportData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "تم التصدير بنجاح",
        description: "تم تصدير سجل العمليات بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ في التصدير",
        description: "فشل في تصدير سجل العمليات",
        variant: "destructive",
      });
    }
  };

  const handleClearOldLogs = () => {
    const removedCount = auditService.clearOldLogs(90); // الاحتفاظ بآخر 90 يوم
    
    if (removedCount > 0) {
      loadLogs();
      loadStats();
      toast({
        title: "تم تنظيف السجلات",
        description: `تم حذف ${removedCount} سجل قديم`,
      });
    } else {
      toast({
        title: "لا توجد سجلات قديمة",
        description: "جميع السجلات حديثة",
      });
    }
  };

  if (!currentUser) {
    return <div>جاري التحميل...</div>;
  }

  if (!hasPermission('system.view.audit')) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">غير مصرح</h3>
            <p className="text-muted-foreground text-center">
              ليس لديك صلاحية لعرض سجل العمليات
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">سجل العمليات</h1>
          <p className="text-muted-foreground mt-2">
            مراقبة وتتبع جميع العمليات والأنشطة في النظام
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="ml-2 h-4 w-4" />
            تصدير السجلات
          </Button>
          <Button variant="outline" onClick={handleClearOldLogs}>
            <Trash2 className="ml-2 h-4 w-4" />
            تنظيف السجلات القديمة
          </Button>
        </div>
      </div>

      {/* إحصائيات سجل العمليات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي العمليات</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              جميع العمليات المسجلة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">العمليات الناجحة</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.successful.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {stats.total > 0 ? ((stats.successful / stats.total) * 100).toFixed(1) : 0}% من المجموع
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">العمليات الفاشلة</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.failed.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {stats.total > 0 ? ((stats.failed / stats.total) * 100).toFixed(1) : 0}% من المجموع
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">اليوم</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.todayCount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              عمليات اليوم
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">هذا الأسبوع</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.weekCount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              عمليات الأسبوع
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">هذا الشهر</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.monthCount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              عمليات الشهر
            </p>
          </CardContent>
        </Card>
      </div>

      {/* مؤشرات الأمان */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            مؤشرات الأمان
          </CardTitle>
          <CardDescription>
            تحليل سريع لحالة الأمان في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {stats.total > 0 ? ((stats.successful / stats.total) * 100).toFixed(1) : 0}%
              </div>
              <div className="text-sm text-muted-foreground">معدل نجاح العمليات</div>
              <Badge variant={stats.successful / stats.total > 0.95 ? "default" : "destructive"} className="mt-2">
                {stats.successful / stats.total > 0.95 ? "ممتاز" : "يحتاج مراجعة"}
              </Badge>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {stats.todayCount}
              </div>
              <div className="text-sm text-muted-foreground">نشاط اليوم</div>
              <Badge variant={stats.todayCount > 0 ? "default" : "secondary"} className="mt-2">
                {stats.todayCount > 0 ? "نشط" : "هادئ"}
              </Badge>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-amber-600">
                {stats.failed}
              </div>
              <div className="text-sm text-muted-foreground">العمليات الفاشلة</div>
              <Badge variant={stats.failed === 0 ? "default" : "destructive"} className="mt-2">
                {stats.failed === 0 ? "آمن" : "يحتاج انتباه"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول سجل العمليات */}
      <Card>
        <CardHeader>
          <CardTitle>سجل العمليات</CardTitle>
          <CardDescription>
            تفاصيل جميع العمليات المسجلة في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>التاريخ والوقت</TableHead>
                <TableHead>المستخدم</TableHead>
                <TableHead>العملية</TableHead>
                <TableHead>النوع</TableHead>
                <TableHead>النتيجة</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logs.slice(0, 50).map((log) => (
                <TableRow key={log.id}>
                  <TableCell className="font-mono text-sm">
                    {new Date(log.timestamp).toLocaleString('ar-EG')}
                  </TableCell>
                  <TableCell>{log.userName}</TableCell>
                  <TableCell>{log.action}</TableCell>
                  <TableCell>{log.resourceType}</TableCell>
                  <TableCell>
                    {log.success ? (
                      <Badge variant="default" className="bg-green-600">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        نجح
                      </Badge>
                    ) : (
                      <Badge variant="destructive">
                        <XCircle className="h-3 w-3 mr-1" />
                        فشل
                      </Badge>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {logs.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد عمليات مسجلة حالياً</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
