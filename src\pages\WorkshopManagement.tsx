import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { productionService } from '@/services/productionService';
import { Workshop, Machine } from '@/types/production';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Factory, 
  Settings, 
  Wrench,
  Users,
  Clock,
  AlertTriangle,
  CheckCircle,
  Activity
} from "lucide-react";

export default function WorkshopManagement() {
  const [workshops, setWorkshops] = useState<Workshop[]>([]);
  const [machines, setMachines] = useState<Machine[]>([]);
  const [selectedWorkshop, setSelectedWorkshop] = useState<Workshop | null>(null);
  const [isWorkshopDialogOpen, setIsWorkshopDialogOpen] = useState(false);
  const [isMachineDialogOpen, setIsMachineDialogOpen] = useState(false);
  const [editingWorkshop, setEditingWorkshop] = useState<Workshop | null>(null);
  const [editingMachine, setEditingMachine] = useState<Machine | null>(null);
  
  const [workshopForm, setWorkshopForm] = useState({
    name: '',
    code: '',
    description: '',
    location: '',
    supervisor: '',
    capacity: 10,
    workingHours: {
      start: '07:00',
      end: '15:00',
      breakStart: '12:00',
      breakEnd: '12:30'
    }
  });

  const [machineForm, setMachineForm] = useState({
    name: '',
    code: '',
    type: '',
    model: '',
    manufacturer: '',
    capacity: 20,
    operatingCost: 100,
    status: 'available' as const
  });

  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setWorkshops(productionService.getWorkshops());
    setMachines(productionService.getMachines());
  };

  const handleAddWorkshop = () => {
    if (!workshopForm.name || !workshopForm.code) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    if (editingWorkshop) {
      // تحديث ورشة موجودة
      const success = productionService.updateWorkshop(editingWorkshop.id, {
        ...workshopForm,
        machines: editingWorkshop.machines,
        currentLoad: editingWorkshop.currentLoad,
        isActive: editingWorkshop.isActive
      });

      if (success) {
        loadData();
        toast({
          title: "تم تحديث الورشة",
          description: `تم تحديث الورشة ${workshopForm.name} بنجاح`,
        });
      }
    } else {
      // إضافة ورشة جديدة
      const newWorkshop = productionService.addWorkshop({
        ...workshopForm,
        machines: [],
        currentLoad: 0,
        isActive: true
      });

      setWorkshops(prev => [...prev, newWorkshop]);
      toast({
        title: "تم إضافة الورشة",
        description: `تم إضافة الورشة ${newWorkshop.name} بنجاح`,
      });
    }

    setIsWorkshopDialogOpen(false);
    setEditingWorkshop(null);
    resetWorkshopForm();
  };

  const handleAddMachine = () => {
    if (!machineForm.name || !machineForm.code || !selectedWorkshop) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة واختيار ورشة",
        variant: "destructive",
      });
      return;
    }

    if (editingMachine) {
      // تحديث مكنة موجودة
      const success = productionService.updateMachine(editingMachine.id, {
        ...machineForm,
        workshopId: selectedWorkshop.id,
        isActive: editingMachine.isActive
      });

      if (success) {
        loadData();
        toast({
          title: "تم تحديث المكنة",
          description: `تم تحديث المكنة ${machineForm.name} بنجاح`,
        });
      }
    } else {
      // إضافة مكنة جديدة
      const newMachine = productionService.addMachine({
        ...machineForm,
        workshopId: selectedWorkshop.id,
        isActive: true
      });

      setMachines(prev => [...prev, newMachine]);
      toast({
        title: "تم إضافة المكنة",
        description: `تم إضافة المكنة ${newMachine.name} بنجاح`,
      });
    }

    setIsMachineDialogOpen(false);
    setEditingMachine(null);
    resetMachineForm();
  };

  const handleEditWorkshop = (workshop: Workshop) => {
    setEditingWorkshop(workshop);
    setWorkshopForm({
      name: workshop.name,
      code: workshop.code,
      description: workshop.description || '',
      location: workshop.location,
      supervisor: workshop.supervisor,
      capacity: workshop.capacity,
      workingHours: workshop.workingHours
    });
    setIsWorkshopDialogOpen(true);
  };

  const handleEditMachine = (machine: Machine) => {
    setEditingMachine(machine);
    setMachineForm({
      name: machine.name,
      code: machine.code,
      type: machine.type,
      model: machine.model,
      manufacturer: machine.manufacturer,
      capacity: machine.capacity,
      operatingCost: machine.operatingCost,
      status: machine.status
    });
    setIsMachineDialogOpen(true);
  };

  const handleDeleteWorkshop = (workshopId: string) => {
    const success = productionService.deleteWorkshop(workshopId);
    if (success) {
      loadData();
      toast({
        title: "تم حذف الورشة",
        description: "تم حذف الورشة بنجاح",
      });
    }
  };

  const handleDeleteMachine = (machineId: string) => {
    const success = productionService.deleteMachine(machineId);
    if (success) {
      loadData();
      toast({
        title: "تم حذف المكنة",
        description: "تم حذف المكنة بنجاح",
      });
    }
  };

  const resetWorkshopForm = () => {
    setWorkshopForm({
      name: '',
      code: '',
      description: '',
      location: '',
      supervisor: '',
      capacity: 10,
      workingHours: {
        start: '07:00',
        end: '15:00',
        breakStart: '12:00',
        breakEnd: '12:30'
      }
    });
  };

  const resetMachineForm = () => {
    setMachineForm({
      name: '',
      code: '',
      type: '',
      model: '',
      manufacturer: '',
      capacity: 20,
      operatingCost: 100,
      status: 'available'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge variant="default" className="bg-green-600">متاحة</Badge>;
      case 'busy':
        return <Badge variant="default" className="bg-blue-600">مشغولة</Badge>;
      case 'maintenance':
        return <Badge variant="default" className="bg-yellow-600">صيانة</Badge>;
      case 'broken':
        return <Badge variant="destructive">معطلة</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getWorkshopMachines = (workshopId: string) => {
    return machines.filter(m => m.workshopId === workshopId);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">إدارة الورش والمكائن</h1>
          <p className="text-muted-foreground mt-2">
            إدارة الورش والمكائن والمعدات الإنتاجية
          </p>
        </div>
        <Button onClick={() => setIsWorkshopDialogOpen(true)}>
          <Plus className="ml-2 h-4 w-4" />
          إضافة ورشة جديدة
        </Button>
      </div>

      <Tabs defaultValue="workshops">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="workshops">الورش</TabsTrigger>
          <TabsTrigger value="machines">المكائن</TabsTrigger>
        </TabsList>

        <TabsContent value="workshops" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {workshops.map((workshop) => {
              const workshopMachines = getWorkshopMachines(workshop.id);
              const availableMachines = workshopMachines.filter(m => m.status === 'available').length;
              const busyMachines = workshopMachines.filter(m => m.status === 'busy').length;
              
              return (
                <Card key={workshop.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Factory className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">{workshop.name}</CardTitle>
                      </div>
                      <Badge variant="outline">{workshop.code}</Badge>
                    </div>
                    <CardDescription>{workshop.location}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">المشرف:</span>
                        <span className="font-medium">{workshop.supervisor}</span>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">السعة:</span>
                        <span className="font-medium">{workshop.currentLoad}/{workshop.capacity} عامل</span>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">المكائن:</span>
                        <div className="flex gap-1">
                          <Badge variant="default" className="bg-green-600 text-xs">
                            {availableMachines} متاحة
                          </Badge>
                          <Badge variant="default" className="bg-blue-600 text-xs">
                            {busyMachines} مشغولة
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">ساعات العمل:</span>
                        <span className="font-medium">
                          {workshop.workingHours.start} - {workshop.workingHours.end}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2 pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedWorkshop(workshop);
                          }}
                        >
                          <Wrench className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditWorkshop(workshop)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteWorkshop(workshop.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="machines" className="space-y-6">
          {selectedWorkshop && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>مكائن ورشة: {selectedWorkshop.name}</CardTitle>
                    <CardDescription>
                      إدارة المكائن والمعدات في الورشة المحددة
                    </CardDescription>
                  </div>
                  <Button onClick={() => setIsMachineDialogOpen(true)}>
                    <Plus className="ml-2 h-4 w-4" />
                    إضافة مكنة
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الكود</TableHead>
                      <TableHead>اسم المكنة</TableHead>
                      <TableHead>النوع</TableHead>
                      <TableHead>الموديل</TableHead>
                      <TableHead>السعة</TableHead>
                      <TableHead>التكلفة/ساعة</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getWorkshopMachines(selectedWorkshop.id).map((machine) => (
                      <TableRow key={machine.id}>
                        <TableCell className="font-mono">{machine.code}</TableCell>
                        <TableCell className="font-medium">{machine.name}</TableCell>
                        <TableCell>{machine.type}</TableCell>
                        <TableCell>{machine.model}</TableCell>
                        <TableCell>{machine.capacity} قطعة/ساعة</TableCell>
                        <TableCell>{machine.operatingCost} ريال</TableCell>
                        <TableCell>{getStatusBadge(machine.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditMachine(machine)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteMachine(machine.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {!selectedWorkshop && (
            <Card>
              <CardContent className="text-center py-8">
                <Factory className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">اختر ورشة لعرض مكائنها</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Dialog إضافة/تعديل ورشة */}
      <Dialog open={isWorkshopDialogOpen} onOpenChange={setIsWorkshopDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editingWorkshop ? 'تعديل الورشة' : 'إضافة ورشة جديدة'}
            </DialogTitle>
            <DialogDescription>
              أدخل بيانات الورشة
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4 max-h-[400px] overflow-y-auto">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="workshop-name">اسم الورشة *</Label>
                <Input
                  id="workshop-name"
                  value={workshopForm.name}
                  onChange={(e) => setWorkshopForm({...workshopForm, name: e.target.value})}
                  placeholder="أدخل اسم الورشة"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="workshop-code">كود الورشة *</Label>
                <Input
                  id="workshop-code"
                  value={workshopForm.code}
                  onChange={(e) => setWorkshopForm({...workshopForm, code: e.target.value})}
                  placeholder="أدخل كود الورشة"
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="workshop-description">الوصف</Label>
              <Textarea
                id="workshop-description"
                value={workshopForm.description}
                onChange={(e) => setWorkshopForm({...workshopForm, description: e.target.value})}
                placeholder="أدخل وصف الورشة"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="workshop-location">الموقع</Label>
                <Input
                  id="workshop-location"
                  value={workshopForm.location}
                  onChange={(e) => setWorkshopForm({...workshopForm, location: e.target.value})}
                  placeholder="أدخل موقع الورشة"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="workshop-supervisor">المشرف</Label>
                <Input
                  id="workshop-supervisor"
                  value={workshopForm.supervisor}
                  onChange={(e) => setWorkshopForm({...workshopForm, supervisor: e.target.value})}
                  placeholder="أدخل اسم المشرف"
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="workshop-capacity">السعة القصوى (عدد العمال)</Label>
              <Input
                id="workshop-capacity"
                type="number"
                value={workshopForm.capacity}
                onChange={(e) => setWorkshopForm({...workshopForm, capacity: parseInt(e.target.value) || 10})}
              />
            </div>
            
            <div className="grid gap-2">
              <Label>ساعات العمل</Label>
              <div className="grid grid-cols-4 gap-2">
                <div>
                  <Label htmlFor="work-start" className="text-xs">البداية</Label>
                  <Input
                    id="work-start"
                    type="time"
                    value={workshopForm.workingHours.start}
                    onChange={(e) => setWorkshopForm({
                      ...workshopForm,
                      workingHours: {...workshopForm.workingHours, start: e.target.value}
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="work-end" className="text-xs">النهاية</Label>
                  <Input
                    id="work-end"
                    type="time"
                    value={workshopForm.workingHours.end}
                    onChange={(e) => setWorkshopForm({
                      ...workshopForm,
                      workingHours: {...workshopForm.workingHours, end: e.target.value}
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="break-start" className="text-xs">بداية الاستراحة</Label>
                  <Input
                    id="break-start"
                    type="time"
                    value={workshopForm.workingHours.breakStart}
                    onChange={(e) => setWorkshopForm({
                      ...workshopForm,
                      workingHours: {...workshopForm.workingHours, breakStart: e.target.value}
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="break-end" className="text-xs">نهاية الاستراحة</Label>
                  <Input
                    id="break-end"
                    type="time"
                    value={workshopForm.workingHours.breakEnd}
                    onChange={(e) => setWorkshopForm({
                      ...workshopForm,
                      workingHours: {...workshopForm.workingHours, breakEnd: e.target.value}
                    })}
                  />
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsWorkshopDialogOpen(false);
              setEditingWorkshop(null);
              resetWorkshopForm();
            }}>
              إلغاء
            </Button>
            <Button onClick={handleAddWorkshop}>
              {editingWorkshop ? 'تحديث الورشة' : 'إضافة الورشة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog إضافة/تعديل مكنة */}
      <Dialog open={isMachineDialogOpen} onOpenChange={setIsMachineDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingMachine ? 'تعديل المكنة' : 'إضافة مكنة جديدة'}
            </DialogTitle>
            <DialogDescription>
              أدخل بيانات المكنة للورشة: {selectedWorkshop?.name}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="machine-name">اسم المكنة *</Label>
                <Input
                  id="machine-name"
                  value={machineForm.name}
                  onChange={(e) => setMachineForm({...machineForm, name: e.target.value})}
                  placeholder="أدخل اسم المكنة"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="machine-code">كود المكنة *</Label>
                <Input
                  id="machine-code"
                  value={machineForm.code}
                  onChange={(e) => setMachineForm({...machineForm, code: e.target.value})}
                  placeholder="أدخل كود المكنة"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="machine-type">نوع المكنة</Label>
                <Select value={machineForm.type} onValueChange={(value) => setMachineForm({...machineForm, type: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع المكنة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="خراطة">خراطة</SelectItem>
                    <SelectItem value="فريزة">فريزة</SelectItem>
                    <SelectItem value="لحام">لحام</SelectItem>
                    <SelectItem value="قطع">قطع</SelectItem>
                    <SelectItem value="ثني">ثني</SelectItem>
                    <SelectItem value="طحن">طحن</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="machine-status">الحالة</Label>
                <Select value={machineForm.status} onValueChange={(value: any) => setMachineForm({...machineForm, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="available">متاحة</SelectItem>
                    <SelectItem value="busy">مشغولة</SelectItem>
                    <SelectItem value="maintenance">صيانة</SelectItem>
                    <SelectItem value="broken">معطلة</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="machine-model">الموديل</Label>
                <Input
                  id="machine-model"
                  value={machineForm.model}
                  onChange={(e) => setMachineForm({...machineForm, model: e.target.value})}
                  placeholder="أدخل موديل المكنة"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="machine-manufacturer">الشركة المصنعة</Label>
                <Input
                  id="machine-manufacturer"
                  value={machineForm.manufacturer}
                  onChange={(e) => setMachineForm({...machineForm, manufacturer: e.target.value})}
                  placeholder="أدخل الشركة المصنعة"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="machine-capacity">السعة الإنتاجية (قطعة/ساعة)</Label>
                <Input
                  id="machine-capacity"
                  type="number"
                  value={machineForm.capacity}
                  onChange={(e) => setMachineForm({...machineForm, capacity: parseInt(e.target.value) || 20})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="machine-cost">تكلفة التشغيل (ريال/ساعة)</Label>
                <Input
                  id="machine-cost"
                  type="number"
                  step="0.01"
                  value={machineForm.operatingCost}
                  onChange={(e) => setMachineForm({...machineForm, operatingCost: parseFloat(e.target.value) || 100})}
                />
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsMachineDialogOpen(false);
              setEditingMachine(null);
              resetMachineForm();
            }}>
              إلغاء
            </Button>
            <Button onClick={handleAddMachine}>
              {editingMachine ? 'تحديث المكنة' : 'إضافة المكنة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
