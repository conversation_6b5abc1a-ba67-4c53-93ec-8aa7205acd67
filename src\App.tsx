import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "@/components/Layout";
import { WorkOrderProvider } from "@/contexts/WorkOrderContext";
import { LocalAuthProvider, useAuth } from "@/contexts/LocalAuthContext";
import { LocalLogin } from "@/components/LocalLogin";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import Dashboard from "./pages/Dashboard";
import Planning from "./pages/Planning";
import Production from "./pages/Production";
import Quality from "./pages/Quality";
import Reports from "./pages/Reports";
import Warehouse from "./pages/Warehouse";
import Users from "./pages/Users";
import AuditLogs from "./pages/AuditLogs";
import AdminDashboard from "./pages/AdminDashboard";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

function AppContent() {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <LocalLogin />;
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/planning" element={
          <ProtectedRoute allowedRoles={['planning']}>
            <Planning />
          </ProtectedRoute>
        } />
        <Route path="/production" element={
          <ProtectedRoute allowedRoles={['production']}>
            <Production />
          </ProtectedRoute>
        } />
        <Route path="/quality" element={
          <ProtectedRoute allowedRoles={['quality']}>
            <Quality />
          </ProtectedRoute>
        } />
        <Route path="/reports" element={<Reports />} />
        <Route path="/warehouse" element={
          <ProtectedRoute allowedRoles={['warehouse']}>
            <Warehouse />
          </ProtectedRoute>
        } />
        <Route path="/users" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Users />
          </ProtectedRoute>
        } />
        <Route path="/audit-logs" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <AuditLogs />
          </ProtectedRoute>
        } />
        <Route path="/admin" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <AdminDashboard />
          </ProtectedRoute>
        } />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Layout>
  );
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <LocalAuthProvider>
        <WorkOrderProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <AppContent />
          </BrowserRouter>
        </WorkOrderProvider>
      </LocalAuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
