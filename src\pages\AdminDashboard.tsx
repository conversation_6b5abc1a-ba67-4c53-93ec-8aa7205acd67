import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/contexts/LocalAuthContext';
import { UserStatsCards } from '@/components/admin/UserStatsCards';
import { SecurityDashboard } from '@/components/admin/SecurityDashboard';
import { auditService } from '@/services/auditService';
import { securityService } from '@/services/securityService';
import { 
  Shield, 
  Users, 
  Activity, 
  Settings,
  BarChart3,
  Clock,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Database,
  HardDrive,
  Cpu,
  Wifi
} from "lucide-react";

export default function AdminDashboard() {
  const { currentUser, users, roles, hasPermission } = useAuth();
  const { toast } = useToast();
  const [systemStats, setSystemStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalRoles: 0,
    auditLogs: 0,
    securityAlerts: 0,
    systemHealth: 95
  });

  useEffect(() => {
    if (currentUser) {
      loadSystemStats();
    }
  }, [currentUser, users, roles]);

  const loadSystemStats = () => {
    const auditStats = auditService.getLogStats();
    const alertStats = securityService.getAlertStats();
    
    setSystemStats({
      totalUsers: users.length,
      activeUsers: users.filter(u => u.status === 'active').length,
      totalRoles: roles.length,
      auditLogs: auditStats.total,
      securityAlerts: alertStats.unacknowledged,
      systemHealth: 95 // يمكن حسابها بناءً على معايير مختلفة
    });
  };

  const handleSystemMaintenance = () => {
    // تنظيف البيانات القديمة
    const auditCleanup = auditService.clearOldLogs(90);
    const securityCleanup = securityService.cleanupOldData();
    
    toast({
      title: "تم تنظيف النظام",
      description: `تم حذف ${auditCleanup} سجل قديم و ${securityCleanup.alertsRemoved} تنبيه قديم`,
    });
    
    loadSystemStats();
  };

  if (!currentUser) {
    return <div>جاري التحميل...</div>;
  }

  if (!hasPermission('users.manage.all')) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">غير مصرح</h3>
            <p className="text-muted-foreground text-center">
              ليس لديك صلاحية لعرض لوحة تحكم المدير
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">لوحة تحكم المدير</h1>
          <p className="text-muted-foreground mt-2">
            مراقبة شاملة للنظام والمستخدمين والأمان
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleSystemMaintenance}>
            <Settings className="ml-2 h-4 w-4" />
            صيانة النظام
          </Button>
        </div>
      </div>

      {/* إحصائيات النظام السريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المستخدمون</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {systemStats.activeUsers} نشط
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأدوار</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.totalRoles}</div>
            <p className="text-xs text-muted-foreground">
              أدوار مختلفة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">سجل العمليات</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.auditLogs.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              عملية مسجلة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">التنبيهات الأمنية</CardTitle>
            <AlertTriangle className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-600">{systemStats.securityAlerts}</div>
            <p className="text-xs text-muted-foreground">
              غير مؤكدة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">صحة النظام</CardTitle>
            <Cpu className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{systemStats.systemHealth}%</div>
            <p className="text-xs text-muted-foreground">
              حالة ممتازة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الاتصال</CardTitle>
            <Wifi className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">متصل</div>
            <p className="text-xs text-muted-foreground">
              جميع الخدمات
            </p>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات الرئيسية */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="users">المستخدمون</TabsTrigger>
          <TabsTrigger value="security">الأمان</TabsTrigger>
          <TabsTrigger value="system">النظام</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* مؤشرات الأداء الرئيسية */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  نشاط المستخدمين
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">نشط اليوم</span>
                    <span className="font-medium">{Math.floor(systemStats.activeUsers * 0.8)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">نشط هذا الأسبوع</span>
                    <span className="font-medium">{Math.floor(systemStats.activeUsers * 0.9)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">نشط هذا الشهر</span>
                    <span className="font-medium">{systemStats.activeUsers}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  إحصائيات العمليات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">عمليات اليوم</span>
                    <span className="font-medium">{auditService.getLogStats().todayCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">عمليات الأسبوع</span>
                    <span className="font-medium">{auditService.getLogStats().weekCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">معدل النجاح</span>
                    <span className="font-medium text-green-600">
                      {((auditService.getLogStats().successful / auditService.getLogStats().total) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-purple-600" />
                  حالة البيانات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">سجلات العمليات</span>
                    <span className="font-medium">{systemStats.auditLogs.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">التنبيهات الأمنية</span>
                    <span className="font-medium">{securityService.getAlertStats().total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">استخدام التخزين</span>
                    <span className="font-medium text-green-600">منخفض</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* التنبيهات المهمة */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                التنبيهات المهمة
              </CardTitle>
              <CardDescription>
                تنبيهات تحتاج انتباه فوري
              </CardDescription>
            </CardHeader>
            <CardContent>
              {systemStats.securityAlerts > 0 ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 border border-amber-200 bg-amber-50 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-amber-600" />
                    <div className="flex-1">
                      <div className="font-medium">تنبيهات أمنية غير مؤكدة</div>
                      <div className="text-sm text-muted-foreground">
                        يوجد {systemStats.securityAlerts} تنبيه أمني يحتاج مراجعة
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      مراجعة
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
                  <p>لا توجد تنبيهات مهمة حالياً</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <UserStatsCards users={users} roles={roles} />
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <SecurityDashboard />
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HardDrive className="h-5 w-5" />
                معلومات النظام
              </CardTitle>
              <CardDescription>
                تفاصيل حالة النظام والأداء
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">الإحصائيات العامة</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">إصدار النظام</span>
                      <Badge variant="outline">v1.0.0</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">وقت التشغيل</span>
                      <span className="text-sm font-medium">24 ساعة</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">آخر تحديث</span>
                      <span className="text-sm font-medium">اليوم</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">الأداء</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">استجابة النظام</span>
                      <Badge variant="default" className="bg-green-600">سريع</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">استخدام الذاكرة</span>
                      <span className="text-sm font-medium">منخفض</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">حالة قاعدة البيانات</span>
                      <Badge variant="default" className="bg-green-600">صحية</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
