// أنواع البيانات لنظام إدارة الإنتاج

export interface Product {
  id: string;
  name: string;
  code: string;
  description?: string;
  category: string;
  unit: string; // وحدة القياس (قطعة، كيلو، متر، إلخ)
  parts: ProductPart[]; // الأجزاء المكونة للمنتج
  estimatedTime: number; // الوقت المقدر للإنتاج (بالدقائق)
  cost: number; // التكلفة المقدرة
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface ProductPart {
  id: string;
  productId: string;
  name: string;
  code: string;
  description?: string;
  quantity: number; // الكمية المطلوبة من هذا الجزء للمنتج الواحد
  unit: string;
  operations: PartOperation[]; // العمليات المطلوبة لهذا الجزء
  material?: string; // نوع المادة الخام
  dimensions?: string; // الأبعاد
  weight?: number; // الوزن
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PartOperation {
  id: string;
  partId: string;
  name: string;
  code: string;
  description?: string;
  sequence: number; // ترتيب العملية
  workshopId: string; // الورشة المطلوبة
  machineId?: string; // المكنة المطلوبة (اختيارية)
  estimatedTime: number; // الوقت المقدر (بالدقائق)
  setupTime: number; // وقت التجهيز (بالدقائق)
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert'; // مستوى المهارة المطلوب
  instructions?: string; // تعليمات العملية
  qualityChecks?: string[]; // فحوصات الجودة المطلوبة
  tools?: string[]; // الأدوات المطلوبة
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Workshop {
  id: string;
  name: string;
  code: string;
  description?: string;
  location: string;
  supervisor: string; // مشرف الورشة
  machines: Machine[]; // المكائن الموجودة في الورشة
  capacity: number; // السعة القصوى للعمال
  currentLoad: number; // الحمولة الحالية
  workingHours: {
    start: string; // وقت بداية العمل
    end: string; // وقت نهاية العمل
    breakStart?: string; // وقت بداية الاستراحة
    breakEnd?: string; // وقت نهاية الاستراحة
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Machine {
  id: string;
  workshopId: string;
  name: string;
  code: string;
  type: string; // نوع المكنة (خراطة، فريزة، لحام، إلخ)
  model: string;
  manufacturer: string;
  specifications?: Record<string, any>; // المواصفات التقنية
  capacity: number; // السعة الإنتاجية (قطعة/ساعة)
  status: 'available' | 'busy' | 'maintenance' | 'broken'; // حالة المكنة
  maintenanceSchedule?: {
    lastMaintenance: string;
    nextMaintenance: string;
    frequency: number; // كل كم يوم
  };
  operatingCost: number; // تكلفة التشغيل (ريال/ساعة)
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WorkOrder {
  id: string;
  orderNumber: string;
  productId: string;
  product?: Product; // للعرض
  customerName: string;
  customerContact?: string;
  quantity: number;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';
  requestedDate: string; // التاريخ المطلوب للتسليم
  startDate?: string; // تاريخ بداية الإنتاج
  completedDate?: string; // تاريخ الانتهاء
  batches: WorkOrderBatch[]; // الدفعات
  notes?: string;
  attachments?: string[]; // مرفقات (رسومات، مواصفات، إلخ)
  totalCost: number; // التكلفة الإجمالية
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface WorkOrderBatch {
  id: string;
  workOrderId: string;
  batchNumber: string;
  quantity: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  startDate?: string;
  completedDate?: string;
  partBatches: PartBatch[]; // دفعات الأجزاء
  assignedTo?: string; // المسؤول عن الدفعة
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PartBatch {
  id: string;
  batchId: string;
  partId: string;
  part?: ProductPart; // للعرض
  quantity: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  operationBatches: OperationBatch[]; // دفعات العمليات
  createdAt: string;
  updatedAt: string;
}

export interface OperationBatch {
  id: string;
  partBatchId: string;
  operationId: string;
  operation?: PartOperation; // للعرض
  workshopId: string;
  workshop?: Workshop; // للعرض
  machineId?: string;
  machine?: Machine; // للعرض
  assignedTo?: string; // العامل المكلف
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  scheduledStart?: string;
  scheduledEnd?: string;
  actualStart?: string;
  actualEnd?: string;
  actualTime?: number; // الوقت الفعلي (بالدقائق)
  qualityStatus?: 'pending' | 'passed' | 'failed' | 'rework';
  qualityNotes?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// إحصائيات الإنتاج
export interface ProductionStats {
  totalProducts: number;
  totalParts: number;
  totalOperations: number;
  totalWorkshops: number;
  totalMachines: number;
  activeWorkOrders: number;
  completedWorkOrders: number;
  pendingWorkOrders: number;
  machineUtilization: number; // نسبة استخدام المكائن
  workshopEfficiency: number; // كفاءة الورش
  onTimeDelivery: number; // نسبة التسليم في الوقت المحدد
}

// فلاتر البحث
export interface ProductionFilters {
  search?: string;
  category?: string;
  status?: string;
  workshop?: string;
  machine?: string;
  priority?: string;
  dateFrom?: string;
  dateTo?: string;
}

// خيارات التصدير
export interface ExportOptions {
  format: 'excel' | 'pdf' | 'csv';
  includeDetails: boolean;
  dateRange?: {
    from: string;
    to: string;
  };
}

// تقرير الإنتاج
export interface ProductionReport {
  id: string;
  title: string;
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  dateRange: {
    from: string;
    to: string;
  };
  data: {
    workOrders: WorkOrder[];
    stats: ProductionStats;
    charts: any[]; // بيانات الرسوم البيانية
  };
  generatedAt: string;
  generatedBy: string;
}

// إعدادات النظام
export interface ProductionSettings {
  defaultWorkingHours: {
    start: string;
    end: string;
    breakStart?: string;
    breakEnd?: string;
  };
  defaultUnits: string[];
  defaultCategories: string[];
  defaultSkillLevels: string[];
  autoNumbering: {
    products: boolean;
    parts: boolean;
    operations: boolean;
    workOrders: boolean;
  };
  notifications: {
    workOrderDeadlines: boolean;
    machineMaintenanceReminders: boolean;
    qualityIssues: boolean;
  };
}
