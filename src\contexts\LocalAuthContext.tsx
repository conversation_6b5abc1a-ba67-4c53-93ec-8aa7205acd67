import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { permissionService } from '@/services/permissionService';
import { auditService, AUDIT_ACTIONS } from '@/services/auditService';
import { Role } from '@/types/permissions';

export interface User {
  id: string;
  name: string;
  email: string;
  role: string; // تغيير إلى string للمرونة
  department: string;
  status: 'active' | 'inactive' | 'locked' | 'expired';
  createdAt: string;
  lastLogin: string;
  passwordExpiry?: string;
  failedLoginAttempts?: number;
  lastFailedLogin?: string;
  mustChangePassword?: boolean;
}

interface AuthContextType {
  currentUser: User | null;
  users: User[];
  roles: Role[];
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string; mustChangePassword?: boolean }>;
  logout: () => void;
  addUser: (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>, password: string) => { success: boolean; message?: string };
  updateUser: (userId: string, userData: Partial<User>, newPassword?: string) => { success: boolean; message?: string };
  deleteUser: (userId: string) => { success: boolean; message?: string };
  changePassword: (userId: string, oldPassword: string, newPassword: string) => { success: boolean; message?: string };
  resetPassword: (userId: string, newPassword: string) => { success: boolean; message?: string };
  lockUser: (userId: string) => { success: boolean; message?: string };
  unlockUser: (userId: string) => { success: boolean; message?: string };
  isAuthenticated: boolean;
  hasPermission: (permissionId: string) => boolean;
  hasAnyPermission: (permissionIds: string[]) => boolean;
  hasRole: (roleId: string) => boolean;
  getUserPermissions: () => string[];
  refreshUserData: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const STORAGE_KEYS = {
  users: 'production_system_users',
  passwords: 'production_system_passwords',
  currentUser: 'production_system_current_user'
};

// المستخدم الافتراضي للإدمن
const defaultAdmin: User = {
  id: 'admin_001',
  name: 'مدير النظام',
  email: '<EMAIL>',
  role: 'admin',
  department: 'إدارة النظام',
  status: 'active',
  createdAt: new Date().toISOString(),
  lastLogin: new Date().toISOString(),
  failedLoginAttempts: 0,
  mustChangePassword: false
};

const defaultPassword = 'admin123';

export function LocalAuthProvider({ children }: { children: ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    initializeSystem();
  }, []);

  const initializeSystem = () => {
    // تحميل المستخدمين
    const storedUsers = localStorage.getItem(STORAGE_KEYS.users);
    const storedPasswords = localStorage.getItem(STORAGE_KEYS.passwords);

    if (!storedUsers || !storedPasswords) {
      // إنشاء النظام لأول مرة مع المستخدم الافتراضي
      const initialUsers = [defaultAdmin];
      const initialPasswords = { [defaultAdmin.id]: defaultPassword };

      localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(initialUsers));
      localStorage.setItem(STORAGE_KEYS.passwords, JSON.stringify(initialPasswords));
      setUsers(initialUsers);

      // تعيين دور المدير للمستخدم الافتراضي
      permissionService.setUserRole(defaultAdmin.id, 'admin');
    } else {
      setUsers(JSON.parse(storedUsers));
    }

    // تحميل الأدوار
    setRoles(permissionService.getAllRoles());

    // التحقق من وجود جلسة مصادقة نشطة
    const storedCurrentUser = localStorage.getItem(STORAGE_KEYS.currentUser);
    if (storedCurrentUser) {
      const user = JSON.parse(storedCurrentUser);
      // التحقق من صحة الجلسة
      if (user.status === 'active') {
        setCurrentUser(user);
      } else {
        localStorage.removeItem(STORAGE_KEYS.currentUser);
      }
    }
  };

  const login = async (email: string, password: string): Promise<{ success: boolean; message?: string; mustChangePassword?: boolean }> => {
    const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
    const storedPasswords = JSON.parse(localStorage.getItem(STORAGE_KEYS.passwords) || '{}');

    const user = storedUsers.find((u: User) => u.email === email);

    if (!user) {
      auditService.log('unknown', email, AUDIT_ACTIONS.USER_LOGIN, 'user', { email }, undefined, false, 'User not found');
      return { success: false, message: 'بيانات تسجيل الدخول غير صحيحة' };
    }

    // التحقق من حالة المستخدم
    if (user.status === 'inactive') {
      auditService.log(user.id, user.name, AUDIT_ACTIONS.USER_LOGIN, 'user', { email }, user.id, false, 'Account inactive');
      return { success: false, message: 'الحساب غير نشط' };
    }

    if (user.status === 'locked') {
      auditService.log(user.id, user.name, AUDIT_ACTIONS.USER_LOGIN, 'user', { email }, user.id, false, 'Account locked');
      return { success: false, message: 'الحساب مقفل. اتصل بالمدير' };
    }

    // التحقق من كلمة المرور
    if (storedPasswords[user.id] !== password) {
      // زيادة عدد محاولات الدخول الفاشلة
      const failedAttempts = (user.failedLoginAttempts || 0) + 1;
      const updatedUser = {
        ...user,
        failedLoginAttempts: failedAttempts,
        lastFailedLogin: new Date().toISOString()
      };

      // قفل الحساب بعد 5 محاولات فاشلة
      if (failedAttempts >= 5) {
        updatedUser.status = 'locked';
      }

      const updatedUsers = storedUsers.map((u: User) => u.id === user.id ? updatedUser : u);
      localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));
      setUsers(updatedUsers);

      auditService.log(user.id, user.name, AUDIT_ACTIONS.USER_LOGIN, 'user', { email, failedAttempts }, user.id, false, 'Invalid password');

      if (failedAttempts >= 5) {
        return { success: false, message: 'تم قفل الحساب بسبب محاولات دخول فاشلة متكررة' };
      }

      return { success: false, message: `كلمة المرور غير صحيحة. المحاولات المتبقية: ${5 - failedAttempts}` };
    }

    // تسجيل دخول ناجح
    const updatedUser = {
      ...user,
      lastLogin: new Date().toISOString(),
      failedLoginAttempts: 0
    };
    const updatedUsers = storedUsers.map((u: User) => u.id === user.id ? updatedUser : u);

    localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));
    localStorage.setItem(STORAGE_KEYS.currentUser, JSON.stringify(updatedUser));

    setCurrentUser(updatedUser);
    setUsers(updatedUsers);

    auditService.log(user.id, user.name, AUDIT_ACTIONS.USER_LOGIN, 'user', { email }, user.id, true);

    return {
      success: true,
      mustChangePassword: user.mustChangePassword || false
    };
  };

  const logout = () => {
    if (currentUser) {
      auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.USER_LOGOUT, 'user', {}, currentUser.id, true);
    }
    localStorage.removeItem(STORAGE_KEYS.currentUser);
    setCurrentUser(null);
  };

  const addUser = (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>, password: string): { success: boolean; message?: string } => {
    if (!currentUser || !permissionService.hasPermission(currentUser.id, 'users.manage.all')) {
      return { success: false, message: 'ليس لديك صلاحية لإضافة المستخدمين' };
    }

    const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
    const storedPasswords = JSON.parse(localStorage.getItem(STORAGE_KEYS.passwords) || '{}');

    // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
    if (storedUsers.some((u: User) => u.email === userData.email)) {
      auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.USER_CREATE, 'user', { email: userData.email }, undefined, false, 'Email already exists');
      return { success: false, message: 'البريد الإلكتروني مستخدم بالفعل' };
    }

    // التحقق من قوة كلمة المرور
    if (password.length < 6) {
      return { success: false, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' };
    }

    const newUser: User = {
      ...userData,
      id: `user_${Date.now()}`,
      createdAt: new Date().toISOString(),
      lastLogin: '',
      failedLoginAttempts: 0,
      mustChangePassword: true // يجب تغيير كلمة المرور في أول تسجيل دخول
    };

    const updatedUsers = [...storedUsers, newUser];
    const updatedPasswords = { ...storedPasswords, [newUser.id]: password };

    localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));
    localStorage.setItem(STORAGE_KEYS.passwords, JSON.stringify(updatedPasswords));

    // تعيين الدور للمستخدم الجديد
    permissionService.setUserRole(newUser.id, userData.role);

    setUsers(updatedUsers);

    auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.USER_CREATE, 'user', {
      newUserId: newUser.id,
      newUserEmail: newUser.email,
      newUserRole: newUser.role
    }, newUser.id, true);

    return { success: true, message: 'تم إضافة المستخدم بنجاح' };
  };

  const updateUser = (userId: string, userData: Partial<User>, newPassword?: string): { success: boolean; message?: string } => {
    if (!currentUser || !permissionService.hasPermission(currentUser.id, 'users.manage.all')) {
      return { success: false, message: 'ليس لديك صلاحية لتعديل المستخدمين' };
    }

    const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
    const targetUser = storedUsers.find((u: User) => u.id === userId);

    if (!targetUser) {
      return { success: false, message: 'المستخدم غير موجود' };
    }

    // التحقق من البريد الإلكتروني إذا تم تغييره
    if (userData.email && userData.email !== targetUser.email) {
      if (storedUsers.some((u: User) => u.email === userData.email && u.id !== userId)) {
        return { success: false, message: 'البريد الإلكتروني مستخدم بالفعل' };
      }
    }

    const updatedUsers = storedUsers.map((u: User) =>
      u.id === userId ? { ...u, ...userData } : u
    );

    localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));

    // تحديث كلمة المرور إذا تم توفيرها
    if (newPassword) {
      if (newPassword.length < 6) {
        return { success: false, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' };
      }

      const storedPasswords = JSON.parse(localStorage.getItem(STORAGE_KEYS.passwords) || '{}');
      storedPasswords[userId] = newPassword;
      localStorage.setItem(STORAGE_KEYS.passwords, JSON.stringify(storedPasswords));

      auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.PASSWORD_RESET, 'user', { targetUserId: userId }, userId, true);
    }

    // تحديث الدور إذا تم تغييره
    if (userData.role && userData.role !== targetUser.role) {
      permissionService.setUserRole(userId, userData.role);
      auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.ROLE_ASSIGN, 'user', {
        targetUserId: userId,
        oldRole: targetUser.role,
        newRole: userData.role
      }, userId, true);
    }

    setUsers(updatedUsers);

    auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.USER_UPDATE, 'user', {
      targetUserId: userId,
      changes: userData
    }, userId, true);

    return { success: true, message: 'تم تحديث المستخدم بنجاح' };
  };

  const deleteUser = (userId: string): { success: boolean; message?: string } => {
    if (!currentUser || !permissionService.hasPermission(currentUser.id, 'users.manage.all')) {
      return { success: false, message: 'ليس لديك صلاحية لحذف المستخدمين' };
    }

    if (userId === currentUser.id) {
      return { success: false, message: 'لا يمكنك حذف حسابك الخاص' };
    }

    const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
    const targetUser = storedUsers.find((u: User) => u.id === userId);

    if (!targetUser) {
      return { success: false, message: 'المستخدم غير موجود' };
    }

    // منع حذف المدير الافتراضي
    if (targetUser.id === 'admin_001') {
      return { success: false, message: 'لا يمكن حذف المدير الافتراضي' };
    }

    const storedPasswords = JSON.parse(localStorage.getItem(STORAGE_KEYS.passwords) || '{}');

    const updatedUsers = storedUsers.filter((u: User) => u.id !== userId);
    delete storedPasswords[userId];

    localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));
    localStorage.setItem(STORAGE_KEYS.passwords, JSON.stringify(storedPasswords));

    setUsers(updatedUsers);

    auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.USER_DELETE, 'user', {
      deletedUserId: userId,
      deletedUserEmail: targetUser.email
    }, userId, true);

    return { success: true, message: 'تم حذف المستخدم بنجاح' };
  };

  // دوال إضافية للأمان
  const changePassword = (userId: string, oldPassword: string, newPassword: string): { success: boolean; message?: string } => {
    if (!currentUser) {
      return { success: false, message: 'يجب تسجيل الدخول أولاً' };
    }

    // المستخدم يمكنه تغيير كلمة مروره الخاصة أو المدير يمكنه تغيير أي كلمة مرور
    if (userId !== currentUser.id && !permissionService.hasPermission(currentUser.id, 'users.manage.all')) {
      return { success: false, message: 'ليس لديك صلاحية لتغيير كلمة المرور' };
    }

    const storedPasswords = JSON.parse(localStorage.getItem(STORAGE_KEYS.passwords) || '{}');

    // التحقق من كلمة المرور القديمة إذا كان المستخدم يغير كلمة مروره الخاصة
    if (userId === currentUser.id && storedPasswords[userId] !== oldPassword) {
      return { success: false, message: 'كلمة المرور القديمة غير صحيحة' };
    }

    if (newPassword.length < 6) {
      return { success: false, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' };
    }

    storedPasswords[userId] = newPassword;
    localStorage.setItem(STORAGE_KEYS.passwords, JSON.stringify(storedPasswords));

    // إزالة علامة وجوب تغيير كلمة المرور
    const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
    const updatedUsers = storedUsers.map((u: User) =>
      u.id === userId ? { ...u, mustChangePassword: false } : u
    );
    localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));
    setUsers(updatedUsers);

    auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.PASSWORD_CHANGE, 'user', { targetUserId: userId }, userId, true);

    return { success: true, message: 'تم تغيير كلمة المرور بنجاح' };
  };

  const resetPassword = (userId: string, newPassword: string): { success: boolean; message?: string } => {
    if (!currentUser || !permissionService.hasPermission(currentUser.id, 'users.manage.all')) {
      return { success: false, message: 'ليس لديك صلاحية لإعادة تعيين كلمة المرور' };
    }

    if (newPassword.length < 6) {
      return { success: false, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' };
    }

    const storedPasswords = JSON.parse(localStorage.getItem(STORAGE_KEYS.passwords) || '{}');
    storedPasswords[userId] = newPassword;
    localStorage.setItem(STORAGE_KEYS.passwords, JSON.stringify(storedPasswords));

    // تعيين وجوب تغيير كلمة المرور في التسجيل التالي
    const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
    const updatedUsers = storedUsers.map((u: User) =>
      u.id === userId ? { ...u, mustChangePassword: true, failedLoginAttempts: 0 } : u
    );
    localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));
    setUsers(updatedUsers);

    auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.PASSWORD_RESET, 'user', { targetUserId: userId }, userId, true);

    return { success: true, message: 'تم إعادة تعيين كلمة المرور بنجاح' };
  };

  const lockUser = (userId: string): { success: boolean; message?: string } => {
    if (!currentUser || !permissionService.hasPermission(currentUser.id, 'users.manage.all')) {
      return { success: false, message: 'ليس لديك صلاحية لقفل المستخدمين' };
    }

    if (userId === currentUser.id) {
      return { success: false, message: 'لا يمكنك قفل حسابك الخاص' };
    }

    const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
    const updatedUsers = storedUsers.map((u: User) =>
      u.id === userId ? { ...u, status: 'locked' as const } : u
    );
    localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));
    setUsers(updatedUsers);

    auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.USER_DEACTIVATE, 'user', { targetUserId: userId }, userId, true);

    return { success: true, message: 'تم قفل المستخدم بنجاح' };
  };

  const unlockUser = (userId: string): { success: boolean; message?: string } => {
    if (!currentUser || !permissionService.hasPermission(currentUser.id, 'users.manage.all')) {
      return { success: false, message: 'ليس لديك صلاحية لإلغاء قفل المستخدمين' };
    }

    const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
    const updatedUsers = storedUsers.map((u: User) =>
      u.id === userId ? { ...u, status: 'active' as const, failedLoginAttempts: 0 } : u
    );
    localStorage.setItem(STORAGE_KEYS.users, JSON.stringify(updatedUsers));
    setUsers(updatedUsers);

    auditService.log(currentUser.id, currentUser.name, AUDIT_ACTIONS.USER_ACTIVATE, 'user', { targetUserId: userId }, userId, true);

    return { success: true, message: 'تم إلغاء قفل المستخدم بنجاح' };
  };

  // دوال الصلاحيات المحدثة
  const hasPermission = (permissionId: string): boolean => {
    if (!currentUser) return false;
    return permissionService.hasPermission(currentUser.id, permissionId);
  };

  const hasAnyPermission = (permissionIds: string[]): boolean => {
    if (!currentUser) return false;
    return permissionService.hasAnyPermission(currentUser.id, permissionIds);
  };

  const hasRole = (roleId: string): boolean => {
    if (!currentUser) return false;
    const userPerms = permissionService.getUserPermissions(currentUser.id);
    return userPerms?.roleId === roleId;
  };

  const getUserPermissions = (): string[] => {
    if (!currentUser) return [];
    return permissionService.getUserEffectivePermissions(currentUser.id);
  };

  const refreshUserData = (): void => {
    if (currentUser) {
      const storedUsers = JSON.parse(localStorage.getItem(STORAGE_KEYS.users) || '[]');
      const updatedUser = storedUsers.find((u: User) => u.id === currentUser.id);
      if (updatedUser) {
        setCurrentUser(updatedUser);
      }
    }
    setRoles(permissionService.getAllRoles());
  };

  return (
    <AuthContext.Provider value={{
      currentUser,
      users,
      roles,
      login,
      logout,
      addUser,
      updateUser,
      deleteUser,
      changePassword,
      resetPassword,
      lockUser,
      unlockUser,
      isAuthenticated: !!currentUser,
      hasPermission,
      hasAnyPermission,
      hasRole,
      getUserPermissions,
      refreshUserData
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within a LocalAuthProvider');
  }
  return context;
}