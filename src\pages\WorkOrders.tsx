import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { productionService } from '@/services/productionService';
import { Product, WorkOrder, WorkOrderBatch } from '@/types/production';
import { 
  Plus, 
  Edit, 
  Trash2, 
  FileText, 
  Calendar, 
  User,
  Package,
  Clock,
  AlertTriangle,
  CheckCircle,
  Play,
  Pause,
  Square
} from "lucide-react";

export default function WorkOrders() {
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedWorkOrder, setSelectedWorkOrder] = useState<WorkOrder | null>(null);
  const [isWorkOrderDialogOpen, setIsWorkOrderDialogOpen] = useState(false);
  const [isBatchDialogOpen, setIsBatchDialogOpen] = useState(false);
  const [editingWorkOrder, setEditingWorkOrder] = useState<WorkOrder | null>(null);
  
  const [workOrderForm, setWorkOrderForm] = useState({
    productId: '',
    customerName: '',
    customerContact: '',
    quantity: 1,
    priority: 'medium' as const,
    requestedDate: '',
    notes: ''
  });

  const [batchForm, setBatchForm] = useState({
    quantity: 1,
    assignedTo: '',
    notes: ''
  });

  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setWorkOrders(productionService.getWorkOrders());
    setProducts(productionService.getProducts());
  };

  const handleAddWorkOrder = () => {
    if (!workOrderForm.productId || !workOrderForm.customerName || !workOrderForm.requestedDate) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    const selectedProduct = products.find(p => p.id === workOrderForm.productId);
    if (!selectedProduct) {
      toast({
        title: "خطأ",
        description: "المنتج المحدد غير موجود",
        variant: "destructive",
      });
      return;
    }

    const newWorkOrder = productionService.addWorkOrder({
      ...workOrderForm,
      status: 'pending',
      batches: [],
      totalCost: selectedProduct.cost * workOrderForm.quantity,
      createdBy: 'current_user' // يجب تمرير المستخدم الحالي
    });

    setWorkOrders(prev => [...prev, newWorkOrder]);
    setIsWorkOrderDialogOpen(false);
    resetWorkOrderForm();
    
    toast({
      title: "تم إضافة أمر الشغل",
      description: `تم إضافة أمر الشغل ${newWorkOrder.orderNumber} بنجاح`,
    });
  };

  const handleAddBatch = () => {
    if (!selectedWorkOrder || !batchForm.quantity) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    // إنشاء دفعة جديدة
    const newBatch: WorkOrderBatch = {
      id: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      workOrderId: selectedWorkOrder.id,
      batchNumber: `B${selectedWorkOrder.batches.length + 1}`,
      quantity: batchForm.quantity,
      status: 'pending',
      partBatches: [],
      assignedTo: batchForm.assignedTo,
      notes: batchForm.notes,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // تحديث أمر الشغل
    const updatedWorkOrder = { ...selectedWorkOrder };
    updatedWorkOrder.batches.push(newBatch);
    setSelectedWorkOrder(updatedWorkOrder);
    
    // تحديث قائمة أوامر الشغل
    setWorkOrders(prev => prev.map(wo => wo.id === selectedWorkOrder.id ? updatedWorkOrder : wo));
    
    setIsBatchDialogOpen(false);
    resetBatchForm();
    
    toast({
      title: "تم إضافة الدفعة",
      description: `تم إضافة الدفعة ${newBatch.batchNumber} بنجاح`,
    });
  };

  const resetWorkOrderForm = () => {
    setWorkOrderForm({
      productId: '',
      customerName: '',
      customerContact: '',
      quantity: 1,
      priority: 'medium',
      requestedDate: '',
      notes: ''
    });
  };

  const resetBatchForm = () => {
    setBatchForm({
      quantity: 1,
      assignedTo: '',
      notes: ''
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">في الانتظار</Badge>;
      case 'in_progress':
        return <Badge variant="default" className="bg-blue-600">قيد التنفيذ</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-600">مكتمل</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>;
      case 'on_hold':
        return <Badge variant="default" className="bg-yellow-600">معلق</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Badge variant="destructive">عاجل</Badge>;
      case 'high':
        return <Badge variant="default" className="bg-orange-600">عالي</Badge>;
      case 'medium':
        return <Badge variant="default" className="bg-blue-600">متوسط</Badge>;
      case 'low':
        return <Badge variant="secondary">منخفض</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'غير محدد';
  };

  const getTotalBatchQuantity = (workOrder: WorkOrder) => {
    return workOrder.batches.reduce((sum, batch) => sum + batch.quantity, 0);
  };

  const getCompletedBatches = (workOrder: WorkOrder) => {
    return workOrder.batches.filter(batch => batch.status === 'completed').length;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">أوامر الشغل</h1>
          <p className="text-muted-foreground mt-2">
            إدارة أوامر الشغل والدفعات الإنتاجية
          </p>
        </div>
        <Button onClick={() => setIsWorkOrderDialogOpen(true)}>
          <Plus className="ml-2 h-4 w-4" />
          إضافة أمر شغل جديد
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الأوامر</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workOrders.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">قيد التنفيذ</CardTitle>
            <Play className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {workOrders.filter(wo => wo.status === 'in_progress').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مكتملة</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {workOrders.filter(wo => wo.status === 'completed').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">في الانتظار</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {workOrders.filter(wo => wo.status === 'pending').length}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="orders">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="orders">أوامر الشغل</TabsTrigger>
          <TabsTrigger value="batches">الدفعات</TabsTrigger>
        </TabsList>

        <TabsContent value="orders" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>قائمة أوامر الشغل</CardTitle>
              <CardDescription>
                جميع أوامر الشغل المسجلة في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>رقم الأمر</TableHead>
                    <TableHead>المنتج</TableHead>
                    <TableHead>العميل</TableHead>
                    <TableHead>الكمية</TableHead>
                    <TableHead>الأولوية</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ التسليم</TableHead>
                    <TableHead>الدفعات</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {workOrders.map((workOrder) => (
                    <TableRow key={workOrder.id}>
                      <TableCell className="font-mono">{workOrder.orderNumber}</TableCell>
                      <TableCell className="font-medium">{getProductName(workOrder.productId)}</TableCell>
                      <TableCell>{workOrder.customerName}</TableCell>
                      <TableCell>{workOrder.quantity}</TableCell>
                      <TableCell>{getPriorityBadge(workOrder.priority)}</TableCell>
                      <TableCell>{getStatusBadge(workOrder.status)}</TableCell>
                      <TableCell>{new Date(workOrder.requestedDate).toLocaleDateString('ar-EG')}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{workOrder.batches.length} دفعة</div>
                          <div className="text-muted-foreground">
                            {getCompletedBatches(workOrder)}/{workOrder.batches.length} مكتملة
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedWorkOrder(workOrder);
                            }}
                          >
                            <Package className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingWorkOrder(workOrder);
                              setWorkOrderForm({
                                productId: workOrder.productId,
                                customerName: workOrder.customerName,
                                customerContact: workOrder.customerContact || '',
                                quantity: workOrder.quantity,
                                priority: workOrder.priority,
                                requestedDate: workOrder.requestedDate.split('T')[0],
                                notes: workOrder.notes || ''
                              });
                              setIsWorkOrderDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="batches" className="space-y-6">
          {selectedWorkOrder ? (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>دفعات أمر الشغل: {selectedWorkOrder.orderNumber}</CardTitle>
                    <CardDescription>
                      إدارة دفعات الإنتاج لأمر الشغل المحدد
                    </CardDescription>
                  </div>
                  <Button onClick={() => setIsBatchDialogOpen(true)}>
                    <Plus className="ml-2 h-4 w-4" />
                    إضافة دفعة
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="mb-4 p-4 bg-muted rounded-lg">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">المنتج:</span>
                      <div className="font-medium">{getProductName(selectedWorkOrder.productId)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">الكمية الإجمالية:</span>
                      <div className="font-medium">{selectedWorkOrder.quantity}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">الكمية المقسمة:</span>
                      <div className="font-medium">{getTotalBatchQuantity(selectedWorkOrder)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">المتبقي:</span>
                      <div className="font-medium text-orange-600">
                        {selectedWorkOrder.quantity - getTotalBatchQuantity(selectedWorkOrder)}
                      </div>
                    </div>
                  </div>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم الدفعة</TableHead>
                      <TableHead>الكمية</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>المسؤول</TableHead>
                      <TableHead>تاريخ البداية</TableHead>
                      <TableHead>تاريخ الانتهاء</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedWorkOrder.batches.map((batch) => (
                      <TableRow key={batch.id}>
                        <TableCell className="font-mono">{batch.batchNumber}</TableCell>
                        <TableCell>{batch.quantity}</TableCell>
                        <TableCell>{getStatusBadge(batch.status)}</TableCell>
                        <TableCell>{batch.assignedTo || '-'}</TableCell>
                        <TableCell>
                          {batch.startDate ? new Date(batch.startDate).toLocaleDateString('ar-EG') : '-'}
                        </TableCell>
                        <TableCell>
                          {batch.completedDate ? new Date(batch.completedDate).toLocaleDateString('ar-EG') : '-'}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {batch.status === 'pending' && (
                              <Button variant="outline" size="sm">
                                <Play className="h-4 w-4" />
                              </Button>
                            )}
                            {batch.status === 'in_progress' && (
                              <Button variant="outline" size="sm">
                                <Pause className="h-4 w-4" />
                              </Button>
                            )}
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">اختر أمر شغل لعرض دفعاته</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Dialog إضافة أمر شغل */}
      <Dialog open={isWorkOrderDialogOpen} onOpenChange={setIsWorkOrderDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingWorkOrder ? 'تعديل أمر الشغل' : 'إضافة أمر شغل جديد'}
            </DialogTitle>
            <DialogDescription>
              أدخل بيانات أمر الشغل
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="wo-product">المنتج *</Label>
              <Select value={workOrderForm.productId} onValueChange={(value) => setWorkOrderForm({...workOrderForm, productId: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر المنتج" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name} ({product.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="wo-customer">اسم العميل *</Label>
                <Input
                  id="wo-customer"
                  value={workOrderForm.customerName}
                  onChange={(e) => setWorkOrderForm({...workOrderForm, customerName: e.target.value})}
                  placeholder="أدخل اسم العميل"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="wo-contact">جهة الاتصال</Label>
                <Input
                  id="wo-contact"
                  value={workOrderForm.customerContact}
                  onChange={(e) => setWorkOrderForm({...workOrderForm, customerContact: e.target.value})}
                  placeholder="رقم الهاتف أو البريد"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="wo-quantity">الكمية *</Label>
                <Input
                  id="wo-quantity"
                  type="number"
                  value={workOrderForm.quantity}
                  onChange={(e) => setWorkOrderForm({...workOrderForm, quantity: parseInt(e.target.value) || 1})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="wo-priority">الأولوية</Label>
                <Select value={workOrderForm.priority} onValueChange={(value: any) => setWorkOrderForm({...workOrderForm, priority: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">منخفض</SelectItem>
                    <SelectItem value="medium">متوسط</SelectItem>
                    <SelectItem value="high">عالي</SelectItem>
                    <SelectItem value="urgent">عاجل</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="wo-date">تاريخ التسليم المطلوب *</Label>
              <Input
                id="wo-date"
                type="date"
                value={workOrderForm.requestedDate}
                onChange={(e) => setWorkOrderForm({...workOrderForm, requestedDate: e.target.value})}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="wo-notes">ملاحظات</Label>
              <Textarea
                id="wo-notes"
                value={workOrderForm.notes}
                onChange={(e) => setWorkOrderForm({...workOrderForm, notes: e.target.value})}
                placeholder="أدخل أي ملاحظات إضافية"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsWorkOrderDialogOpen(false);
              setEditingWorkOrder(null);
              resetWorkOrderForm();
            }}>
              إلغاء
            </Button>
            <Button onClick={handleAddWorkOrder}>
              {editingWorkOrder ? 'تحديث الأمر' : 'إضافة الأمر'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog إضافة دفعة */}
      <Dialog open={isBatchDialogOpen} onOpenChange={setIsBatchDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>إضافة دفعة جديدة</DialogTitle>
            <DialogDescription>
              أدخل بيانات الدفعة لأمر الشغل: {selectedWorkOrder?.orderNumber}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="batch-quantity">الكمية *</Label>
              <Input
                id="batch-quantity"
                type="number"
                value={batchForm.quantity}
                onChange={(e) => setBatchForm({...batchForm, quantity: parseInt(e.target.value) || 1})}
                max={selectedWorkOrder ? selectedWorkOrder.quantity - getTotalBatchQuantity(selectedWorkOrder) : undefined}
              />
              {selectedWorkOrder && (
                <div className="text-xs text-muted-foreground">
                  الحد الأقصى: {selectedWorkOrder.quantity - getTotalBatchQuantity(selectedWorkOrder)}
                </div>
              )}
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="batch-assigned">المسؤول عن الدفعة</Label>
              <Input
                id="batch-assigned"
                value={batchForm.assignedTo}
                onChange={(e) => setBatchForm({...batchForm, assignedTo: e.target.value})}
                placeholder="اسم المسؤول"
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="batch-notes">ملاحظات</Label>
              <Textarea
                id="batch-notes"
                value={batchForm.notes}
                onChange={(e) => setBatchForm({...batchForm, notes: e.target.value})}
                placeholder="أدخل أي ملاحظات"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsBatchDialogOpen(false);
              resetBatchForm();
            }}>
              إلغاء
            </Button>
            <Button onClick={handleAddBatch}>
              إضافة الدفعة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
