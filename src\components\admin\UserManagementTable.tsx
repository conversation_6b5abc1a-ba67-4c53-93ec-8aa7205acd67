import { useState, useMemo } from 'react';
import { User } from '@/contexts/LocalAuthContext';
import { Role } from '@/types/permissions';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  MoreHorizontal, 
  Search, 
  Filter, 
  UserCheck, 
  UserX, 
  Key, 
  Edit, 
  Trash2,
  Shield,
  Clock,
  AlertTriangle
} from "lucide-react";

interface UserManagementTableProps {
  users: User[];
  roles: Role[];
  currentUser: User;
  onEditUser: (user: User) => void;
  onDeleteUser: (userId: string) => void;
  onResetPassword: (user: User) => void;
  onLockUser: (userId: string) => void;
  onUnlockUser: (userId: string) => void;
}

interface FilterState {
  search: string;
  role: string;
  status: string;
  department: string;
}

const getStatusBadge = (status: User['status']) => {
  const variants = {
    active: { variant: 'default' as const, label: 'نشط', icon: UserCheck },
    inactive: { variant: 'secondary' as const, label: 'غير نشط', icon: UserX },
    locked: { variant: 'destructive' as const, label: 'مقفل', icon: Shield },
    expired: { variant: 'outline' as const, label: 'منتهي الصلاحية', icon: Clock }
  };
  
  const config = variants[status] || variants.inactive;
  const Icon = config.icon;
  
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
};

const getRoleBadge = (roleId: string, roles: Role[]) => {
  const role = roles.find(r => r.id === roleId);
  const variants = {
    admin: 'destructive' as const,
    planning_manager: 'default' as const,
    production_manager: 'default' as const,
    quality_manager: 'default' as const,
    warehouse_manager: 'default' as const,
  };
  
  return (
    <Badge variant={variants[roleId as keyof typeof variants] || 'outline'}>
      {role?.displayName || roleId}
    </Badge>
  );
};

export function UserManagementTable({
  users,
  roles,
  currentUser,
  onEditUser,
  onDeleteUser,
  onResetPassword,
  onLockUser,
  onUnlockUser
}: UserManagementTableProps) {
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    role: '',
    status: '',
    department: ''
  });

  const [sortField, setSortField] = useState<keyof User>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // الحصول على القيم الفريدة للفلاتر
  const departments = useMemo(() => 
    [...new Set(users.map(u => u.department))].filter(Boolean),
    [users]
  );

  // تطبيق الفلاتر والترتيب
  const filteredAndSortedUsers = useMemo(() => {
    let filtered = users.filter(user => {
      const matchesSearch = !filters.search || 
        user.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        user.email.toLowerCase().includes(filters.search.toLowerCase());
      
      const matchesRole = !filters.role || user.role === filters.role;
      const matchesStatus = !filters.status || user.status === filters.status;
      const matchesDepartment = !filters.department || user.department === filters.department;
      
      return matchesSearch && matchesRole && matchesStatus && matchesDepartment;
    });

    // الترتيب
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [users, filters, sortField, sortDirection]);

  const handleSort = (field: keyof User) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      role: '',
      status: '',
      department: ''
    });
  };

  return (
    <div className="space-y-4">
      {/* شريط البحث والفلاتر */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="البحث في الأسماء والإيميلات..."
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Select value={filters.role} onValueChange={(value) => setFilters(prev => ({ ...prev, role: value }))}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="الدور" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">جميع الأدوار</SelectItem>
              {roles.map(role => (
                <SelectItem key={role.id} value={role.id}>
                  {role.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="الحالة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">جميع الحالات</SelectItem>
              <SelectItem value="active">نشط</SelectItem>
              <SelectItem value="inactive">غير نشط</SelectItem>
              <SelectItem value="locked">مقفل</SelectItem>
              <SelectItem value="expired">منتهي الصلاحية</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.department} onValueChange={(value) => setFilters(prev => ({ ...prev, department: value }))}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="القسم" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">جميع الأقسام</SelectItem>
              {departments.map(dept => (
                <SelectItem key={dept} value={dept}>
                  {dept}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={clearFilters}>
            <Filter className="h-4 w-4 mr-2" />
            مسح الفلاتر
          </Button>
        </div>
      </div>

      {/* الجدول */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">#</TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('name')}
              >
                المستخدم
                {sortField === 'name' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('role')}
              >
                الدور
                {sortField === 'role' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('department')}
              >
                القسم
                {sortField === 'department' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('status')}
              >
                الحالة
                {sortField === 'status' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('lastLogin')}
              >
                آخر دخول
                {sortField === 'lastLogin' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </TableHead>
              <TableHead className="w-12">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedUsers.map((user, index) => (
              <TableRow key={user.id}>
                <TableCell>{index + 1}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {user.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium flex items-center gap-2">
                        {user.name}
                        {user.mustChangePassword && (
                          <AlertTriangle className="h-4 w-4 text-amber-500" title="يجب تغيير كلمة المرور" />
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {getRoleBadge(user.role, roles)}
                </TableCell>
                <TableCell>{user.department}</TableCell>
                <TableCell>
                  {getStatusBadge(user.status)}
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {user.lastLogin ? 
                      new Date(user.lastLogin).toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      }) : 
                      'لم يسجل الدخول'
                    }
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      
                      <DropdownMenuItem onClick={() => onEditUser(user)}>
                        <Edit className="mr-2 h-4 w-4" />
                        تعديل
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem onClick={() => onResetPassword(user)}>
                        <Key className="mr-2 h-4 w-4" />
                        إعادة تعيين كلمة المرور
                      </DropdownMenuItem>
                      
                      {user.status === 'active' ? (
                        <DropdownMenuItem 
                          onClick={() => onLockUser(user.id)}
                          className="text-amber-600"
                        >
                          <Shield className="mr-2 h-4 w-4" />
                          قفل المستخدم
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem 
                          onClick={() => onUnlockUser(user.id)}
                          className="text-green-600"
                        >
                          <UserCheck className="mr-2 h-4 w-4" />
                          إلغاء القفل
                        </DropdownMenuItem>
                      )}
                      
                      {user.id !== currentUser.id && user.id !== 'admin_001' && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => onDeleteUser(user.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            حذف
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {filteredAndSortedUsers.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            لا توجد نتائج تطابق البحث
          </div>
        )}
      </div>

      {/* معلومات الجدول */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div>
          عرض {filteredAndSortedUsers.length} من {users.length} مستخدم
        </div>
        <div>
          {filters.search || filters.role || filters.status || filters.department ? (
            <span>تم تطبيق فلاتر</span>
          ) : null}
        </div>
      </div>
    </div>
  );
}
