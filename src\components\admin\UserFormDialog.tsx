import { useState, useEffect } from 'react';
import { User } from '@/contexts/LocalAuthContext';
import { Role } from '@/types/permissions';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  User as UserIcon, 
  Shield, 
  Key, 
  AlertCircle, 
  CheckCircle,
  Info
} from "lucide-react";

interface UserFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
  roles: Role[];
  onSave: (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>, password?: string) => { success: boolean; message?: string };
}

interface FormData {
  name: string;
  email: string;
  role: string;
  department: string;
  status: User['status'];
  password: string;
  confirmPassword: string;
  mustChangePassword: boolean;
}

interface FormErrors {
  name?: string;
  email?: string;
  role?: string;
  department?: string;
  password?: string;
  confirmPassword?: string;
}

export function UserFormDialog({ open, onOpenChange, user, roles, onSave }: UserFormDialogProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    role: '',
    department: '',
    status: 'active',
    password: '',
    confirmPassword: '',
    mustChangePassword: true
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  // تحديث النموذج عند تغيير المستخدم
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        role: user.role,
        department: user.department,
        status: user.status,
        password: '',
        confirmPassword: '',
        mustChangePassword: user.mustChangePassword || false
      });
    } else {
      setFormData({
        name: '',
        email: '',
        role: '',
        department: '',
        status: 'active',
        password: '',
        confirmPassword: '',
        mustChangePassword: true
      });
    }
    setErrors({});
    setActiveTab('basic');
  }, [user, open]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // التحقق من الاسم
    if (!formData.name.trim()) {
      newErrors.name = 'الاسم مطلوب';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'الاسم يجب أن يكون حرفين على الأقل';
    }

    // التحقق من البريد الإلكتروني
    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من الدور
    if (!formData.role) {
      newErrors.role = 'الدور مطلوب';
    }

    // التحقق من القسم
    if (!formData.department.trim()) {
      newErrors.department = 'القسم مطلوب';
    }

    // التحقق من كلمة المرور (للمستخدمين الجدد أو عند التغيير)
    if (!user && !formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة للمستخدمين الجدد';
    } else if (formData.password && formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    // التحقق من تأكيد كلمة المرور
    if (formData.password && formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمات المرور غير متطابقة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'> = {
        name: formData.name.trim(),
        email: formData.email.trim().toLowerCase(),
        role: formData.role,
        department: formData.department.trim(),
        status: formData.status,
        mustChangePassword: formData.mustChangePassword,
        failedLoginAttempts: 0
      };

      const result = onSave(userData, formData.password || undefined);
      
      if (result.success) {
        onOpenChange(false);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // مسح الخطأ عند التعديل
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const selectedRole = roles.find(r => r.id === formData.role);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserIcon className="h-5 w-5" />
            {user ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}
          </DialogTitle>
          <DialogDescription>
            {user ? 'قم بتحديث بيانات المستخدم أدناه.' : 'أدخل بيانات المستخدم الجديد أدناه.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">البيانات الأساسية</TabsTrigger>
              <TabsTrigger value="role">الدور والصلاحيات</TabsTrigger>
              <TabsTrigger value="security">الأمان</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">الاسم الكامل *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="أدخل الاسم الكامل"
                    className={errors.name ? 'border-destructive' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">البريد الإلكتروني *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className={errors.email ? 'border-destructive' : ''}
                    dir="ltr"
                  />
                  {errors.email && (
                    <p className="text-sm text-destructive">{errors.email}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="department">القسم *</Label>
                  <Input
                    id="department"
                    value={formData.department}
                    onChange={(e) => handleInputChange('department', e.target.value)}
                    placeholder="اسم القسم"
                    className={errors.department ? 'border-destructive' : ''}
                  />
                  {errors.department && (
                    <p className="text-sm text-destructive">{errors.department}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">حالة الحساب</Label>
                  <Select 
                    value={formData.status} 
                    onValueChange={(value: User['status']) => handleInputChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">نشط</SelectItem>
                      <SelectItem value="inactive">غير نشط</SelectItem>
                      <SelectItem value="locked">مقفل</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="role" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="role">الدور *</Label>
                <Select 
                  value={formData.role} 
                  onValueChange={(value) => handleInputChange('role', value)}
                >
                  <SelectTrigger className={errors.role ? 'border-destructive' : ''}>
                    <SelectValue placeholder="اختر دور المستخدم" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map(role => (
                      <SelectItem key={role.id} value={role.id}>
                        <div className="flex items-center gap-2">
                          <span>{role.displayName}</span>
                          {role.isSystemRole && (
                            <Badge variant="outline" className="text-xs">نظامي</Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.role && (
                  <p className="text-sm text-destructive">{errors.role}</p>
                )}
              </div>

              {selectedRole && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      معلومات الدور
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <p className="text-sm font-medium">{selectedRole.displayName}</p>
                      <p className="text-xs text-muted-foreground">{selectedRole.description}</p>
                    </div>
                    
                    <div>
                      <p className="text-xs font-medium mb-2">الصلاحيات ({selectedRole.permissions.length}):</p>
                      <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                        {selectedRole.permissions.slice(0, 10).map(permId => (
                          <Badge key={permId} variant="secondary" className="text-xs">
                            {permId.split('.').pop()}
                          </Badge>
                        ))}
                        {selectedRole.permissions.length > 10 && (
                          <Badge variant="outline" className="text-xs">
                            +{selectedRole.permissions.length - 10} أخرى
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="security" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password">
                    كلمة المرور {!user && '*'}
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    placeholder={user ? 'اتركها فارغة لعدم التغيير' : 'كلمة المرور الجديدة'}
                    className={errors.password ? 'border-destructive' : ''}
                    dir="ltr"
                  />
                  {errors.password && (
                    <p className="text-sm text-destructive">{errors.password}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    placeholder="أعد إدخال كلمة المرور"
                    className={errors.confirmPassword ? 'border-destructive' : ''}
                    dir="ltr"
                  />
                  {errors.confirmPassword && (
                    <p className="text-sm text-destructive">{errors.confirmPassword}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  id="mustChangePassword"
                  checked={formData.mustChangePassword}
                  onCheckedChange={(checked) => handleInputChange('mustChangePassword', checked)}
                />
                <Label htmlFor="mustChangePassword" className="text-sm">
                  يجب تغيير كلمة المرور في التسجيل التالي
                </Label>
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  <strong>نصائح الأمان:</strong>
                  <ul className="mt-2 space-y-1 text-xs">
                    <li>• استخدم كلمة مرور قوية تحتوي على 6 أحرف على الأقل</li>
                    <li>• فعّل "يجب تغيير كلمة المرور" للمستخدمين الجدد</li>
                    <li>• راجع صلاحيات الدور بعناية قبل الحفظ</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              إلغاء
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'جاري الحفظ...' : (user ? 'تحديث' : 'إضافة')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
