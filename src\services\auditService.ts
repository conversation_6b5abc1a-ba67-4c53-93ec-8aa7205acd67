export interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
  success: boolean;
  errorMessage?: string;
}

export interface AuditFilter {
  userId?: string;
  action?: string;
  resource?: string;
  startDate?: string;
  endDate?: string;
  success?: boolean;
}

export class AuditService {
  private static instance: AuditService;
  private logs: AuditLog[] = [];
  private readonly STORAGE_KEY = 'system_audit_logs';
  private readonly MAX_LOGS = 10000; // الحد الأقصى لعدد السجلات

  private constructor() {
    this.loadLogs();
  }

  public static getInstance(): AuditService {
    if (!AuditService.instance) {
      AuditService.instance = new AuditService();
    }
    return AuditService.instance;
  }

  private loadLogs(): void {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (stored) {
      this.logs = JSON.parse(stored);
    }
  }

  private saveLogs(): void {
    // الاحتفاظ بآخر MAX_LOGS سجل فقط
    if (this.logs.length > this.MAX_LOGS) {
      this.logs = this.logs.slice(-this.MAX_LOGS);
    }
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.logs));
  }

  public log(
    userId: string,
    userName: string,
    action: string,
    resource: string,
    details: Record<string, any> = {},
    resourceId?: string,
    success: boolean = true,
    errorMessage?: string
  ): void {
    const logEntry: AuditLog = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      userName,
      action,
      resource,
      resourceId,
      details,
      timestamp: new Date().toISOString(),
      success,
      errorMessage
    };

    this.logs.push(logEntry);
    this.saveLogs();
  }

  public getLogs(filter?: AuditFilter, limit?: number, offset?: number): AuditLog[] {
    let filteredLogs = [...this.logs];

    if (filter) {
      if (filter.userId) {
        filteredLogs = filteredLogs.filter(log => log.userId === filter.userId);
      }
      if (filter.action) {
        filteredLogs = filteredLogs.filter(log => log.action.includes(filter.action!));
      }
      if (filter.resource) {
        filteredLogs = filteredLogs.filter(log => log.resource.includes(filter.resource!));
      }
      if (filter.startDate) {
        filteredLogs = filteredLogs.filter(log => log.timestamp >= filter.startDate!);
      }
      if (filter.endDate) {
        filteredLogs = filteredLogs.filter(log => log.timestamp <= filter.endDate!);
      }
      if (filter.success !== undefined) {
        filteredLogs = filteredLogs.filter(log => log.success === filter.success);
      }
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // تطبيق التصفح
    if (offset !== undefined && limit !== undefined) {
      return filteredLogs.slice(offset, offset + limit);
    }
    if (limit !== undefined) {
      return filteredLogs.slice(0, limit);
    }

    return filteredLogs;
  }

  public getLogById(id: string): AuditLog | undefined {
    return this.logs.find(log => log.id === id);
  }

  public getLogsByUser(userId: string, limit?: number): AuditLog[] {
    return this.getLogs({ userId }, limit);
  }

  public getLogsByAction(action: string, limit?: number): AuditLog[] {
    return this.getLogs({ action }, limit);
  }

  public getLogsByResource(resource: string, limit?: number): AuditLog[] {
    return this.getLogs({ resource }, limit);
  }

  public getFailedLogs(limit?: number): AuditLog[] {
    return this.getLogs({ success: false }, limit);
  }

  public getRecentLogs(limit: number = 100): AuditLog[] {
    return this.getLogs(undefined, limit);
  }

  public getLogStats(): {
    total: number;
    successful: number;
    failed: number;
    todayCount: number;
    weekCount: number;
    monthCount: number;
  } {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    return {
      total: this.logs.length,
      successful: this.logs.filter(log => log.success).length,
      failed: this.logs.filter(log => !log.success).length,
      todayCount: this.logs.filter(log => new Date(log.timestamp) >= today).length,
      weekCount: this.logs.filter(log => new Date(log.timestamp) >= weekAgo).length,
      monthCount: this.logs.filter(log => new Date(log.timestamp) >= monthAgo).length
    };
  }

  public clearOldLogs(daysToKeep: number = 90): number {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const initialCount = this.logs.length;
    this.logs = this.logs.filter(log => new Date(log.timestamp) >= cutoffDate);
    
    const removedCount = initialCount - this.logs.length;
    if (removedCount > 0) {
      this.saveLogs();
    }
    
    return removedCount;
  }

  public exportLogs(filter?: AuditFilter): string {
    const logs = this.getLogs(filter);
    return JSON.stringify(logs, null, 2);
  }

  public searchLogs(query: string, limit?: number): AuditLog[] {
    const lowerQuery = query.toLowerCase();
    const filtered = this.logs.filter(log => 
      log.userName.toLowerCase().includes(lowerQuery) ||
      log.action.toLowerCase().includes(lowerQuery) ||
      log.resource.toLowerCase().includes(lowerQuery) ||
      JSON.stringify(log.details).toLowerCase().includes(lowerQuery)
    );

    // ترتيب حسب التاريخ (الأحدث أولاً)
    filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return limit ? filtered.slice(0, limit) : filtered;
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const auditService = AuditService.getInstance();

// دوال مساعدة لتسجيل العمليات الشائعة
export const logUserAction = (
  userId: string,
  userName: string,
  action: string,
  details: Record<string, any> = {},
  success: boolean = true,
  errorMessage?: string
) => {
  auditService.log(userId, userName, action, 'user', details, userId, success, errorMessage);
};

export const logSystemAction = (
  userId: string,
  userName: string,
  action: string,
  details: Record<string, any> = {},
  success: boolean = true,
  errorMessage?: string
) => {
  auditService.log(userId, userName, action, 'system', details, undefined, success, errorMessage);
};

export const logPermissionAction = (
  userId: string,
  userName: string,
  action: string,
  targetUserId: string,
  details: Record<string, any> = {},
  success: boolean = true,
  errorMessage?: string
) => {
  auditService.log(userId, userName, action, 'permission', details, targetUserId, success, errorMessage);
};

// أنواع العمليات الشائعة
export const AUDIT_ACTIONS = {
  // User Management
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  USER_CREATE: 'user.create',
  USER_UPDATE: 'user.update',
  USER_DELETE: 'user.delete',
  USER_ACTIVATE: 'user.activate',
  USER_DEACTIVATE: 'user.deactivate',
  PASSWORD_CHANGE: 'user.password_change',
  PASSWORD_RESET: 'user.password_reset',

  // Role Management
  ROLE_CREATE: 'role.create',
  ROLE_UPDATE: 'role.update',
  ROLE_DELETE: 'role.delete',
  ROLE_ASSIGN: 'role.assign',
  ROLE_REVOKE: 'role.revoke',

  // Permission Management
  PERMISSION_GRANT: 'permission.grant',
  PERMISSION_REVOKE: 'permission.revoke',
  PERMISSION_UPDATE: 'permission.update',

  // System Actions
  SYSTEM_BACKUP: 'system.backup',
  SYSTEM_RESTORE: 'system.restore',
  SYSTEM_SETTINGS_UPDATE: 'system.settings_update',
  SYSTEM_MAINTENANCE: 'system.maintenance',

  // Data Actions
  DATA_EXPORT: 'data.export',
  DATA_IMPORT: 'data.import',
  DATA_DELETE: 'data.delete',
  DATA_BULK_UPDATE: 'data.bulk_update'
} as const;
