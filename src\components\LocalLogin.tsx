import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/LocalAuthContext';
import { Factory, Shield, AlertCircle } from 'lucide-react';

export function LocalLogin() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await login(email, password);
      if (!result.success) {
        setError(result.message || 'بيانات تسجيل الدخول غير صحيحة');
      }
      // إذا نجح تسجيل الدخول، سيتم إعادة توجيه المستخدم تلقائياً
    } catch (err) {
      setError('حدث خطأ أثناء تسجيل الدخول');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = () => {
    setEmail('<EMAIL>');
    setPassword('admin123');
  };

  const handleClearData = () => {
    // مسح جميع البيانات المحفوظة
    localStorage.removeItem('current_user');
    localStorage.removeItem('users');
    localStorage.removeItem('passwords');
    localStorage.removeItem('system_permissions');
    localStorage.removeItem('system_roles');
    localStorage.removeItem('user_permissions');
    localStorage.removeItem('system_audit_logs');
    localStorage.removeItem('security_settings');
    localStorage.removeItem('security_alerts');

    // إعادة تحميل الصفحة
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* شعار النظام */}
        <div className="text-center space-y-2">
          <div className="mx-auto w-16 h-16 bg-primary rounded-full flex items-center justify-center">
            <Factory className="h-8 w-8 text-primary-foreground" />
          </div>
          <h1 className="text-2xl font-bold text-foreground">نظام إدارة الإنتاج</h1>
          <p className="text-muted-foreground">النظام المحلي الآمن</p>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center">تسجيل الدخول</CardTitle>
            <CardDescription className="text-center">
              أدخل بياناتك للوصول إلى النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="أدخل البريد الإلكتروني"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  dir="ltr"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">كلمة المرور</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="أدخل كلمة المرور"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  dir="ltr"
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
              </Button>
            </form>

            {/* معلومات الحساب التجريبي */}
            <div className="mt-6 pt-6 border-t">
              <div className="text-center space-y-3">
                <p className="text-sm text-muted-foreground">للتجربة:</p>
                <Button
                  variant="outline"
                  onClick={handleDemoLogin}
                  className="w-full"
                >
                  <Shield className="mr-2 h-4 w-4" />
                  استخدام الحساب التجريبي
                </Button>
                <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
                  <p><strong>البريد:</strong> <EMAIL></p>
                  <p><strong>كلمة المرور:</strong> admin123</p>
                </div>

                {/* زر مسح البيانات */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearData}
                  className="w-full text-xs text-muted-foreground hover:text-destructive"
                >
                  مسح جميع البيانات وإعادة التشغيل
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* معلومات النظام المحلي */}
        <Card className="bg-accent/50">
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <Shield className="mx-auto h-8 w-8 text-success" />
              <h3 className="font-semibold text-success">نظام محلي آمن</h3>
              <p className="text-xs text-muted-foreground">
                جميع بياناتك محفوظة محلياً على جهازك ولا تخرج عبر الإنترنت
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}