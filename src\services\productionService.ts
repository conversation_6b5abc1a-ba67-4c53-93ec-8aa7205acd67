import { 
  Product, 
  ProductPart, 
  PartOperation, 
  Workshop, 
  Machine, 
  WorkOrder, 
  WorkOrderBatch,
  PartBatch,
  OperationBatch,
  ProductionStats,
  ProductionFilters 
} from '@/types/production';

class ProductionService {
  private static instance: ProductionService;
  
  // مفاتيح التخزين المحلي
  private readonly STORAGE_KEYS = {
    products: 'production_products',
    parts: 'production_parts',
    operations: 'production_operations',
    workshops: 'production_workshops',
    machines: 'production_machines',
    workOrders: 'production_work_orders',
    batches: 'production_batches',
    settings: 'production_settings'
  };

  private constructor() {
    this.initializeDefaultData();
  }

  public static getInstance(): ProductionService {
    if (!ProductionService.instance) {
      ProductionService.instance = new ProductionService();
    }
    return ProductionService.instance;
  }

  // تهيئة البيانات الافتراضية
  private initializeDefaultData(): void {
    // إنشاء ورش افتراضية إذا لم تكن موجودة
    if (!localStorage.getItem(this.STORAGE_KEYS.workshops)) {
      const defaultWorkshops: Workshop[] = [
        {
          id: 'ws_001',
          name: 'ورشة الخراطة',
          code: 'LATHE',
          location: 'المبنى الأول - الطابق الأرضي',
          supervisor: 'أحمد محمد',
          machines: [],
          capacity: 10,
          currentLoad: 0,
          workingHours: {
            start: '07:00',
            end: '15:00',
            breakStart: '12:00',
            breakEnd: '12:30'
          },
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'ws_002',
          name: 'ورشة الفريزة',
          code: 'MILL',
          location: 'المبنى الأول - الطابق الأول',
          supervisor: 'محمد علي',
          machines: [],
          capacity: 8,
          currentLoad: 0,
          workingHours: {
            start: '07:00',
            end: '15:00',
            breakStart: '12:00',
            breakEnd: '12:30'
          },
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'ws_003',
          name: 'ورشة اللحام',
          code: 'WELD',
          location: 'المبنى الثاني',
          supervisor: 'علي أحمد',
          machines: [],
          capacity: 6,
          currentLoad: 0,
          workingHours: {
            start: '07:00',
            end: '15:00',
            breakStart: '12:00',
            breakEnd: '12:30'
          },
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      this.saveWorkshops(defaultWorkshops);
    }

    // إنشاء مكائن افتراضية
    if (!localStorage.getItem(this.STORAGE_KEYS.machines)) {
      const defaultMachines: Machine[] = [
        {
          id: 'mc_001',
          workshopId: 'ws_001',
          name: 'مخرطة CNC رقم 1',
          code: 'CNC_L001',
          type: 'خراطة',
          model: 'DMG MORI NLX 2500',
          manufacturer: 'DMG MORI',
          capacity: 20,
          status: 'available',
          operatingCost: 150,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'mc_002',
          workshopId: 'ws_002',
          name: 'فريزة CNC رقم 1',
          code: 'CNC_M001',
          type: 'فريزة',
          model: 'HAAS VF-2',
          manufacturer: 'HAAS',
          capacity: 15,
          status: 'available',
          operatingCost: 180,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'mc_003',
          workshopId: 'ws_003',
          name: 'ماكينة لحام MIG',
          code: 'MIG_001',
          type: 'لحام',
          model: 'Lincoln Electric',
          manufacturer: 'Lincoln',
          capacity: 25,
          status: 'available',
          operatingCost: 100,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      this.saveMachines(defaultMachines);
    }
  }

  // إدارة المنتجات
  public getProducts(): Product[] {
    const stored = localStorage.getItem(this.STORAGE_KEYS.products);
    return stored ? JSON.parse(stored) : [];
  }

  public getProductById(id: string): Product | undefined {
    return this.getProducts().find(p => p.id === id);
  }

  public addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Product {
    const products = this.getProducts();
    const newProduct: Product = {
      ...product,
      id: `prod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    products.push(newProduct);
    this.saveProducts(products);
    return newProduct;
  }

  public updateProduct(id: string, updates: Partial<Product>): boolean {
    const products = this.getProducts();
    const index = products.findIndex(p => p.id === id);
    
    if (index === -1) return false;
    
    products[index] = {
      ...products[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    this.saveProducts(products);
    return true;
  }

  public deleteProduct(id: string): boolean {
    const products = this.getProducts();
    const filtered = products.filter(p => p.id !== id);
    
    if (filtered.length === products.length) return false;
    
    this.saveProducts(filtered);
    return true;
  }

  // إدارة الأجزاء
  public getPartsByProductId(productId: string): ProductPart[] {
    const parts = this.getParts();
    return parts.filter(p => p.productId === productId);
  }

  public getParts(): ProductPart[] {
    const stored = localStorage.getItem(this.STORAGE_KEYS.parts);
    return stored ? JSON.parse(stored) : [];
  }

  public addPart(part: Omit<ProductPart, 'id' | 'createdAt' | 'updatedAt'>): ProductPart {
    const parts = this.getParts();
    const newPart: ProductPart = {
      ...part,
      id: `part_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    parts.push(newPart);
    this.saveParts(parts);
    return newPart;
  }

  // إدارة العمليات
  public getOperationsByPartId(partId: string): PartOperation[] {
    const operations = this.getOperations();
    return operations.filter(o => o.partId === partId);
  }

  public getOperations(): PartOperation[] {
    const stored = localStorage.getItem(this.STORAGE_KEYS.operations);
    return stored ? JSON.parse(stored) : [];
  }

  public addOperation(operation: Omit<PartOperation, 'id' | 'createdAt' | 'updatedAt'>): PartOperation {
    const operations = this.getOperations();
    const newOperation: PartOperation = {
      ...operation,
      id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    operations.push(newOperation);
    this.saveOperations(operations);
    return newOperation;
  }

  // إدارة الورش
  public getWorkshops(): Workshop[] {
    const stored = localStorage.getItem(this.STORAGE_KEYS.workshops);
    return stored ? JSON.parse(stored) : [];
  }

  public getWorkshopById(id: string): Workshop | undefined {
    return this.getWorkshops().find(w => w.id === id);
  }

  public addWorkshop(workshop: Omit<Workshop, 'id' | 'createdAt' | 'updatedAt'>): Workshop {
    const workshops = this.getWorkshops();
    const newWorkshop: Workshop = {
      ...workshop,
      id: `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    workshops.push(newWorkshop);
    this.saveWorkshops(workshops);
    return newWorkshop;
  }

  public updateWorkshop(id: string, updates: Partial<Workshop>): boolean {
    const workshops = this.getWorkshops();
    const index = workshops.findIndex(w => w.id === id);

    if (index === -1) return false;

    workshops[index] = {
      ...workshops[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.saveWorkshops(workshops);
    return true;
  }

  public deleteWorkshop(id: string): boolean {
    const workshops = this.getWorkshops();
    const filtered = workshops.filter(w => w.id !== id);

    if (filtered.length === workshops.length) return false;

    this.saveWorkshops(filtered);
    return true;
  }

  // إدارة المكائن
  public getMachines(): Machine[] {
    const stored = localStorage.getItem(this.STORAGE_KEYS.machines);
    return stored ? JSON.parse(stored) : [];
  }

  public getMachinesByWorkshopId(workshopId: string): Machine[] {
    return this.getMachines().filter(m => m.workshopId === workshopId);
  }

  public addMachine(machine: Omit<Machine, 'id' | 'createdAt' | 'updatedAt'>): Machine {
    const machines = this.getMachines();
    const newMachine: Machine = {
      ...machine,
      id: `mc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    machines.push(newMachine);
    this.saveMachines(machines);
    return newMachine;
  }

  public updateMachine(id: string, updates: Partial<Machine>): boolean {
    const machines = this.getMachines();
    const index = machines.findIndex(m => m.id === id);

    if (index === -1) return false;

    machines[index] = {
      ...machines[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.saveMachines(machines);
    return true;
  }

  public deleteMachine(id: string): boolean {
    const machines = this.getMachines();
    const filtered = machines.filter(m => m.id !== id);

    if (filtered.length === machines.length) return false;

    this.saveMachines(filtered);
    return true;
  }

  // إدارة أوامر الشغل
  public getWorkOrders(): WorkOrder[] {
    const stored = localStorage.getItem(this.STORAGE_KEYS.workOrders);
    return stored ? JSON.parse(stored) : [];
  }

  public addWorkOrder(workOrder: Omit<WorkOrder, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>): WorkOrder {
    const workOrders = this.getWorkOrders();
    const orderNumber = `WO${Date.now().toString().slice(-6)}`;
    
    const newWorkOrder: WorkOrder = {
      ...workOrder,
      id: `wo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      orderNumber,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    workOrders.push(newWorkOrder);
    this.saveWorkOrders(workOrders);
    return newWorkOrder;
  }

  // الإحصائيات
  public getProductionStats(): ProductionStats {
    const products = this.getProducts();
    const parts = this.getParts();
    const operations = this.getOperations();
    const workshops = this.getWorkshops();
    const machines = this.getMachines();
    const workOrders = this.getWorkOrders();

    return {
      totalProducts: products.length,
      totalParts: parts.length,
      totalOperations: operations.length,
      totalWorkshops: workshops.length,
      totalMachines: machines.length,
      activeWorkOrders: workOrders.filter(wo => wo.status === 'in_progress').length,
      completedWorkOrders: workOrders.filter(wo => wo.status === 'completed').length,
      pendingWorkOrders: workOrders.filter(wo => wo.status === 'pending').length,
      machineUtilization: this.calculateMachineUtilization(),
      workshopEfficiency: this.calculateWorkshopEfficiency(),
      onTimeDelivery: this.calculateOnTimeDelivery()
    };
  }

  // حسابات الإحصائيات
  private calculateMachineUtilization(): number {
    const machines = this.getMachines();
    if (machines.length === 0) return 0;
    
    const busyMachines = machines.filter(m => m.status === 'busy').length;
    return (busyMachines / machines.length) * 100;
  }

  private calculateWorkshopEfficiency(): number {
    // حساب كفاءة الورش بناءً على الحمولة الحالية مقابل السعة
    const workshops = this.getWorkshops();
    if (workshops.length === 0) return 0;
    
    const totalCapacity = workshops.reduce((sum, w) => sum + w.capacity, 0);
    const totalLoad = workshops.reduce((sum, w) => sum + w.currentLoad, 0);
    
    return totalCapacity > 0 ? (totalLoad / totalCapacity) * 100 : 0;
  }

  private calculateOnTimeDelivery(): number {
    const completedOrders = this.getWorkOrders().filter(wo => wo.status === 'completed');
    if (completedOrders.length === 0) return 100;
    
    const onTimeOrders = completedOrders.filter(wo => {
      if (!wo.completedDate) return false;
      return new Date(wo.completedDate) <= new Date(wo.requestedDate);
    });
    
    return (onTimeOrders.length / completedOrders.length) * 100;
  }

  // دوال الحفظ الخاصة
  private saveProducts(products: Product[]): void {
    localStorage.setItem(this.STORAGE_KEYS.products, JSON.stringify(products));
  }

  private saveParts(parts: ProductPart[]): void {
    localStorage.setItem(this.STORAGE_KEYS.parts, JSON.stringify(parts));
  }

  private saveOperations(operations: PartOperation[]): void {
    localStorage.setItem(this.STORAGE_KEYS.operations, JSON.stringify(operations));
  }

  private saveWorkshops(workshops: Workshop[]): void {
    localStorage.setItem(this.STORAGE_KEYS.workshops, JSON.stringify(workshops));
  }

  private saveMachines(machines: Machine[]): void {
    localStorage.setItem(this.STORAGE_KEYS.machines, JSON.stringify(machines));
  }

  private saveWorkOrders(workOrders: WorkOrder[]): void {
    localStorage.setItem(this.STORAGE_KEYS.workOrders, JSON.stringify(workOrders));
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const productionService = ProductionService.getInstance();
