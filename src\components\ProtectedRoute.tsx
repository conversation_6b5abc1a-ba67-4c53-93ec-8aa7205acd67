import { ReactNode } from 'react';
import { useAuth } from '@/contexts/LocalAuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  allowedRoles?: string[];
  requiredPermissions?: string[];
  fallback?: ReactNode;
}

export function ProtectedRoute({
  children,
  allowedRoles = [],
  requiredPermissions = [],
  fallback
}: ProtectedRouteProps) {
  const { currentUser, hasPermission, hasAnyPermission, hasRole } = useAuth();

  if (!currentUser) {
    return null; // سيتم التعامل مع هذا في App.tsx
  }

  // التحقق من الأدوار (للتوافق مع النظام القديم)
  const hasRequiredRole = allowedRoles.length === 0 || allowedRoles.some(role => hasRole(role));

  // التحقق من الصلاحيات الجديدة
  const hasRequiredPermissions = requiredPermissions.length === 0 || hasAnyPermission(requiredPermissions);

  if (!hasRequiredRole && !hasRequiredPermissions) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-center">
              <div className="space-y-2">
                <p className="font-semibold">ليس لديك صلاحية للوصول</p>
                {allowedRoles.length > 0 && (
                  <p className="text-sm">
                    الأدوار المطلوبة: {allowedRoles.join(', ')}
                  </p>
                )}
                {requiredPermissions.length > 0 && (
                  <p className="text-sm">
                    الصلاحيات المطلوبة: {requiredPermissions.join(', ')}
                  </p>
                )}
                <p className="text-sm">
                  دورك الحالي: <span className="font-medium">{currentUser.role}</span>
                </p>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

interface RoleGuardProps {
  allowedRoles?: string[];
  requiredPermissions?: string[];
  children: ReactNode;
  fallback?: ReactNode;
}

export function RoleGuard({ allowedRoles = [], requiredPermissions = [], children, fallback }: RoleGuardProps) {
  const { hasRole, hasAnyPermission } = useAuth();

  const hasRequiredRole = allowedRoles.length === 0 || allowedRoles.some(role => hasRole(role));
  const hasRequiredPermissions = requiredPermissions.length === 0 || hasAnyPermission(requiredPermissions);

  if (!hasRequiredRole && !hasRequiredPermissions) {
    return fallback || (
      <div className="text-center py-8">
        <Shield className="mx-auto h-12 w-12 text-muted-foreground mb-2" />
        <p className="text-muted-foreground">
          ليس لديك صلاحية لرؤية هذا المحتوى
        </p>
      </div>
    );
  }

  return <>{children}</>;
}

// مكون جديد للتحقق من صلاحية واحدة
interface PermissionGuardProps {
  permission: string;
  children: ReactNode;
  fallback?: ReactNode;
}

export function PermissionGuard({ permission, children, fallback }: PermissionGuardProps) {
  const { hasPermission } = useAuth();

  if (!hasPermission(permission)) {
    return fallback || null;
  }

  return <>{children}</>;
}