import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock,
  Key,
  Users,
  Activity,
  Settings,
  TrendingUp,
  TrendingDown
} from "lucide-react";
import { securityService, SecurityAlert } from '@/services/securityService';

interface SecurityDashboardProps {
  className?: string;
}

export function SecurityDashboard({ className }: SecurityDashboardProps) {
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [alertStats, setAlertStats] = useState({
    total: 0,
    unacknowledged: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    byType: {} as Record<string, number>
  });
  const [securityCheck, setSecurityCheck] = useState({
    score: 0,
    issues: [] as Array<{ severity: string; message: string; recommendation: string }>
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setAlerts(securityService.getAlerts({ limit: 10 }));
    setAlertStats(securityService.getAlertStats());
    setSecurityCheck(securityService.performSecurityCheck());
  };

  const handleAcknowledgeAlert = (alertId: string) => {
    securityService.acknowledgeAlert(alertId, 'current_user'); // يجب تمرير المستخدم الحالي
    loadData();
  };

  const getAlertIcon = (type: SecurityAlert['type']) => {
    switch (type) {
      case 'failed_login': return Users;
      case 'account_locked': return Shield;
      case 'permission_escalation': return AlertTriangle;
      case 'suspicious_activity': return Activity;
      case 'password_expired': return Key;
      default: return AlertTriangle;
    }
  };

  const getAlertColor = (severity: SecurityAlert['severity']) => {
    switch (severity) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getSeverityBadge = (severity: SecurityAlert['severity']) => {
    const variants = {
      critical: 'destructive' as const,
      high: 'destructive' as const,
      medium: 'default' as const,
      low: 'secondary' as const
    };
    
    const labels = {
      critical: 'حرج',
      high: 'عالي',
      medium: 'متوسط',
      low: 'منخفض'
    };

    return (
      <Badge variant={variants[severity]}>
        {labels[severity]}
      </Badge>
    );
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 90) return 'ممتاز';
    if (score >= 70) return 'جيد';
    if (score >= 50) return 'متوسط';
    return 'ضعيف';
  };

  return (
    <div className={className}>
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="alerts">التنبيهات</TabsTrigger>
          <TabsTrigger value="analysis">التحليل</TabsTrigger>
          <TabsTrigger value="settings">الإعدادات</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* نقاط الأمان */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                نقاط الأمان العامة
              </CardTitle>
              <CardDescription>
                تقييم شامل لحالة الأمان في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className={`text-3xl font-bold ${getScoreColor(securityCheck.score)}`}>
                      {securityCheck.score}/100
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {getScoreLabel(securityCheck.score)}
                    </div>
                  </div>
                  <div className="flex-1 mx-6">
                    <Progress value={securityCheck.score} className="h-3" />
                  </div>
                  <div className="text-right">
                    {securityCheck.score >= 90 ? (
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    ) : securityCheck.score >= 70 ? (
                      <AlertTriangle className="h-8 w-8 text-yellow-600" />
                    ) : (
                      <XCircle className="h-8 w-8 text-red-600" />
                    )}
                  </div>
                </div>

                {securityCheck.issues.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      تم العثور على {securityCheck.issues.length} مشكلة أمنية تحتاج إلى انتباه
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>

          {/* إحصائيات التنبيهات */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي التنبيهات</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{alertStats.total}</div>
                <p className="text-xs text-muted-foreground">
                  جميع التنبيهات الأمنية
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">غير مؤكدة</CardTitle>
                <Clock className="h-4 w-4 text-amber-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">{alertStats.unacknowledged}</div>
                <p className="text-xs text-muted-foreground">
                  تحتاج مراجعة
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">حرجة</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{alertStats.critical}</div>
                <p className="text-xs text-muted-foreground">
                  تحتاج تدخل فوري
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">عالية</CardTitle>
                <TrendingUp className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{alertStats.high}</div>
                <p className="text-xs text-muted-foreground">
                  أولوية عالية
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">متوسطة ومنخفضة</CardTitle>
                <TrendingDown className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{alertStats.medium + alertStats.low}</div>
                <p className="text-xs text-muted-foreground">
                  أولوية منخفضة
                </p>
              </CardContent>
            </Card>
          </div>

          {/* آخر التنبيهات */}
          <Card>
            <CardHeader>
              <CardTitle>آخر التنبيهات الأمنية</CardTitle>
              <CardDescription>
                أحدث 5 تنبيهات أمنية في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.slice(0, 5).map((alert) => {
                  const AlertIcon = getAlertIcon(alert.type);
                  const alertColor = getAlertColor(alert.severity);
                  
                  return (
                    <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <AlertIcon className={`h-5 w-5 ${alertColor}`} />
                        <div>
                          <div className="font-medium">{alert.message}</div>
                          <div className="text-sm text-muted-foreground">
                            {alert.userName && `${alert.userName} • `}
                            {new Date(alert.timestamp).toLocaleString('ar-EG')}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getSeverityBadge(alert.severity)}
                        {!alert.acknowledged && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleAcknowledgeAlert(alert.id)}
                          >
                            تأكيد
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
                
                {alerts.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
                    <p>لا توجد تنبيهات أمنية حالياً</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>جميع التنبيهات الأمنية</CardTitle>
              <CardDescription>
                قائمة شاملة بجميع التنبيهات الأمنية مع إمكانية التصفية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => {
                  const AlertIcon = getAlertIcon(alert.type);
                  const alertColor = getAlertColor(alert.severity);
                  
                  return (
                    <div key={alert.id} className={`p-4 border rounded-lg ${alert.acknowledged ? 'opacity-60' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <AlertIcon className={`h-5 w-5 mt-0.5 ${alertColor}`} />
                          <div className="flex-1">
                            <div className="font-medium">{alert.message}</div>
                            <div className="text-sm text-muted-foreground mt-1">
                              {alert.userName && `المستخدم: ${alert.userName} • `}
                              {new Date(alert.timestamp).toLocaleString('ar-EG')}
                            </div>
                            {Object.keys(alert.details).length > 0 && (
                              <div className="mt-2 text-xs bg-muted p-2 rounded">
                                <pre>{JSON.stringify(alert.details, null, 2)}</pre>
                              </div>
                            )}
                            {alert.acknowledged && (
                              <div className="text-xs text-green-600 mt-2">
                                تم التأكيد بواسطة {alert.acknowledgedBy} في {new Date(alert.acknowledgedAt!).toLocaleString('ar-EG')}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getSeverityBadge(alert.severity)}
                          {!alert.acknowledged && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => handleAcknowledgeAlert(alert.id)}
                            >
                              تأكيد
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>تحليل المشاكل الأمنية</CardTitle>
              <CardDescription>
                تفاصيل المشاكل المكتشفة والتوصيات لحلها
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {securityCheck.issues.map((issue, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className={`h-5 w-5 mt-0.5 ${
                        issue.severity === 'high' ? 'text-red-600' : 
                        issue.severity === 'medium' ? 'text-yellow-600' : 
                        'text-blue-600'
                      }`} />
                      <div className="flex-1">
                        <div className="font-medium">{issue.message}</div>
                        <div className="text-sm text-muted-foreground mt-1">
                          التوصية: {issue.recommendation}
                        </div>
                      </div>
                      <Badge variant={
                        issue.severity === 'high' ? 'destructive' : 
                        issue.severity === 'medium' ? 'default' : 
                        'secondary'
                      }>
                        {issue.severity === 'high' ? 'عالي' : 
                         issue.severity === 'medium' ? 'متوسط' : 'منخفض'}
                      </Badge>
                    </div>
                  </div>
                ))}
                
                {securityCheck.issues.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
                    <p>لا توجد مشاكل أمنية مكتشفة</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إعدادات الأمان
              </CardTitle>
              <CardDescription>
                تكوين سياسات الأمان والحماية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>واجهة إعدادات الأمان قيد التطوير</p>
                <p className="text-sm">سيتم إضافة إعدادات تفصيلية للأمان قريباً</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
