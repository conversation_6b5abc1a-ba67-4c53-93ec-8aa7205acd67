
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

const STORAGE_KEYS = {
  workOrders: 'production_system_work_orders',
  productionEntries: 'production_system_production_entries',
  qualityEntries: 'production_system_quality_entries',
  parts: 'production_system_parts',
  operations: 'production_system_operations'
};

export interface WorkOrder {
  id: string;
  orderNumber: string;
  productName: string;
  quantity: number;
  startDate: string;
  endDate: string;
  status: 'planned' | 'in_progress' | 'completed' | 'delayed';
  createdBy: string;
  createdAt: string;
}

export interface ProductionEntry {
  id: string;
  workOrderId: string;
  date: string;
  producedQuantity: number;
  acceptedQuantity: number;
  rejectedQuantity: number;
  machineName: string;
  workshop: string;
  workerName: string;
  enteredBy: string;
}

export interface QualityEntry {
  id: string;
  workOrderId: string;
  date: string;
  producedQuantity: number;
  firstTimeRightQuantity: number;
  rejectedQuantity: number;
  acceptedQuantity: number;
  firstTimeRightPercentage: number;
  defects: DefectEntry[];
  deliveredToWarehouse: number;
  enteredBy: string;
}

export interface DefectEntry {
  id: string;
  defectName: string;
  quantity: number;
  repair: string;
}

export interface Part {
  id: string;
  workOrderId: string;
  partName: string;
  quantity: number;
  createdAt: string;
}

export interface Operation {
  id: string;
  partId: string;
  operationName: string;
  status: 'pending' | 'in_progress' | 'completed';
  assignedTo: string;
  startedAt?: string;
  completedAt?: string;
  createdAt: string;
}

interface WorkOrderContextType {
  workOrders: WorkOrder[];
  productionEntries: ProductionEntry[];
  qualityEntries: QualityEntry[];
  parts: Part[];
  operations: Operation[];
  addWorkOrder: (order: Omit<WorkOrder, 'id' | 'createdAt'>) => void;
  addProductionEntry: (entry: Omit<ProductionEntry, 'id'>) => void;
  addQualityEntry: (entry: Omit<QualityEntry, 'id'>) => void;
  addPart: (part: Omit<Part, 'id' | 'createdAt'>) => void;
  addOperation: (operation: Omit<Operation, 'id' | 'createdAt'>) => void;
  getWorkOrderById: (id: string) => WorkOrder | undefined;
  getProductionByWorkOrder: (workOrderId: string) => ProductionEntry[];
  getQualityByWorkOrder: (workOrderId: string) => QualityEntry[];
  getPartsByWorkOrder: (workOrderId: string) => Part[];
  getOperationsByPart: (partId: string) => Operation[];
  updateWorkOrderStatus: (id: string, status: WorkOrder['status']) => void;
  updateOperationStatus: (id: string, status: Operation['status']) => void;
  exportData: () => string;
  importData: (jsonData: string) => boolean;
  clearAllData: () => void;
}

const WorkOrderContext = createContext<WorkOrderContextType | undefined>(undefined);

export function WorkOrderProvider({ children }: { children: ReactNode }) {
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [productionEntries, setProductionEntries] = useState<ProductionEntry[]>([]);
  const [qualityEntries, setQualityEntries] = useState<QualityEntry[]>([]);
  const [parts, setParts] = useState<Part[]>([]);
  const [operations, setOperations] = useState<Operation[]>([]);

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    loadDataFromStorage();
  }, []);

  const loadDataFromStorage = () => {
    const storedWorkOrders = localStorage.getItem(STORAGE_KEYS.workOrders);
    const storedProductionEntries = localStorage.getItem(STORAGE_KEYS.productionEntries);
    const storedQualityEntries = localStorage.getItem(STORAGE_KEYS.qualityEntries);
    const storedParts = localStorage.getItem(STORAGE_KEYS.parts);
    const storedOperations = localStorage.getItem(STORAGE_KEYS.operations);

    if (storedWorkOrders) {
      setWorkOrders(JSON.parse(storedWorkOrders));
    }
    if (storedProductionEntries) {
      setProductionEntries(JSON.parse(storedProductionEntries));
    }
    if (storedQualityEntries) {
      setQualityEntries(JSON.parse(storedQualityEntries));
    }
    if (storedParts) {
      setParts(JSON.parse(storedParts));
    }
    if (storedOperations) {
      setOperations(JSON.parse(storedOperations));
    }
  };

  const addWorkOrder = (order: Omit<WorkOrder, 'id' | 'createdAt'>) => {
    const newOrder: WorkOrder = {
      ...order,
      id: `wo_${Date.now()}`,
      createdAt: new Date().toISOString(),
    };
    const updatedOrders = [...workOrders, newOrder];
    setWorkOrders(updatedOrders);
    localStorage.setItem(STORAGE_KEYS.workOrders, JSON.stringify(updatedOrders));
  };

  const addProductionEntry = (entry: Omit<ProductionEntry, 'id'>) => {
    const newEntry: ProductionEntry = {
      ...entry,
      id: `prod_${Date.now()}`,
    };
    const updatedEntries = [...productionEntries, newEntry];
    setProductionEntries(updatedEntries);
    localStorage.setItem(STORAGE_KEYS.productionEntries, JSON.stringify(updatedEntries));
  };

  const addQualityEntry = (entry: Omit<QualityEntry, 'id'>) => {
    const newEntry: QualityEntry = {
      ...entry,
      id: `qual_${Date.now()}`,
    };
    const updatedEntries = [...qualityEntries, newEntry];
    setQualityEntries(updatedEntries);
    localStorage.setItem(STORAGE_KEYS.qualityEntries, JSON.stringify(updatedEntries));
  };

  const addPart = (part: Omit<Part, 'id' | 'createdAt'>) => {
    const newPart: Part = {
      ...part,
      id: `part_${Date.now()}`,
      createdAt: new Date().toISOString(),
    };
    const updatedParts = [...parts, newPart];
    setParts(updatedParts);
    localStorage.setItem(STORAGE_KEYS.parts, JSON.stringify(updatedParts));
  };

  const addOperation = (operation: Omit<Operation, 'id' | 'createdAt'>) => {
    const newOperation: Operation = {
      ...operation,
      id: `op_${Date.now()}`,
      createdAt: new Date().toISOString(),
    };
    const updatedOperations = [...operations, newOperation];
    setOperations(updatedOperations);
    localStorage.setItem(STORAGE_KEYS.operations, JSON.stringify(updatedOperations));
  };

  const getWorkOrderById = (id: string) => {
    return workOrders.find(order => order.id === id);
  };

  const getProductionByWorkOrder = (workOrderId: string) => {
    return productionEntries.filter(entry => entry.workOrderId === workOrderId);
  };

  const getQualityByWorkOrder = (workOrderId: string) => {
    return qualityEntries.filter(entry => entry.workOrderId === workOrderId);
  };

  const getPartsByWorkOrder = (workOrderId: string) => {
    return parts.filter(part => part.workOrderId === workOrderId);
  };

  const getOperationsByPart = (partId: string) => {
    return operations.filter(operation => operation.partId === partId);
  };

  const updateWorkOrderStatus = (id: string, status: WorkOrder['status']) => {
    const updatedOrders = workOrders.map(order => 
      order.id === id ? { ...order, status } : order
    );
    setWorkOrders(updatedOrders);
    localStorage.setItem(STORAGE_KEYS.workOrders, JSON.stringify(updatedOrders));
  };

  const updateOperationStatus = (id: string, status: Operation['status']) => {
    const updatedOperations = operations.map(op =>
      op.id === id ? { ...op, status } : op
    );
    setOperations(updatedOperations);
    localStorage.setItem(STORAGE_KEYS.operations, JSON.stringify(updatedOperations));
  };

  // وظائف إضافية للنسخ الاحتياطي والاستيراد
  const exportData = () => {
    const data = {
      workOrders,
      productionEntries,
      qualityEntries,
      parts,
      operations,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };
    return JSON.stringify(data, null, 2);
  };

  const importData = (jsonData: string) => {
    try {
      const data = JSON.parse(jsonData);
      if (data.workOrders) {
        setWorkOrders(data.workOrders);
        localStorage.setItem(STORAGE_KEYS.workOrders, JSON.stringify(data.workOrders));
      }
      if (data.productionEntries) {
        setProductionEntries(data.productionEntries);
        localStorage.setItem(STORAGE_KEYS.productionEntries, JSON.stringify(data.productionEntries));
      }
      if (data.qualityEntries) {
        setQualityEntries(data.qualityEntries);
        localStorage.setItem(STORAGE_KEYS.qualityEntries, JSON.stringify(data.qualityEntries));
      }
      if (data.parts) {
        setParts(data.parts);
        localStorage.setItem(STORAGE_KEYS.parts, JSON.stringify(data.parts));
      }
      if (data.operations) {
        setOperations(data.operations);
        localStorage.setItem(STORAGE_KEYS.operations, JSON.stringify(data.operations));
      }
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  };

  const clearAllData = () => {
    setWorkOrders([]);
    setProductionEntries([]);
    setQualityEntries([]);
    setParts([]);
    setOperations([]);
    localStorage.removeItem(STORAGE_KEYS.workOrders);
    localStorage.removeItem(STORAGE_KEYS.productionEntries);
    localStorage.removeItem(STORAGE_KEYS.qualityEntries);
    localStorage.removeItem(STORAGE_KEYS.parts);
    localStorage.removeItem(STORAGE_KEYS.operations);
  };

  return (
    <WorkOrderContext.Provider value={{
      workOrders,
      productionEntries,
      qualityEntries,
      parts,
      operations,
      addWorkOrder,
      addProductionEntry,
      addQualityEntry,
      addPart,
      addOperation,
      getWorkOrderById,
      getProductionByWorkOrder,
      getQualityByWorkOrder,
      getPartsByWorkOrder,
      getOperationsByPart,
      updateWorkOrderStatus,
      updateOperationStatus,
      exportData,
      importData,
      clearAllData,
    }}>
      {children}
    </WorkOrderContext.Provider>
  );
}

export function useWorkOrders() {
  const context = useContext(WorkOrderContext);
  if (context === undefined) {
    throw new Error('useWorkOrders must be used within a WorkOrderProvider');
  }
  return context;
}
