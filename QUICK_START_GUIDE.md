# دليل البدء السريع - نظام إدارة المستخدمين والصلاحيات

## البدء السريع

### 1. تشغيل النظام
```bash
npm run dev
```
الوصول للنظام: `http://localhost:8080`

### 2. تسجيل الدخول الأولي
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `admin123`

## الوظائف الأساسية

### إدارة المستخدمين

#### إضافة مستخدم جديد
1. انتقل إلى "المستخدمين" من الشريط الجانبي
2. اضغط "إضافة مستخدم جديد"
3. املأ البيانات المطلوبة:
   - الاسم الكامل
   - البريد الإلكتروني
   - كلمة المرور (8 أحرف على الأقل)
   - الدور (اختر من القائمة)
   - القسم
4. اضغط "إضافة"

#### تعديل مستخدم موجود
1. في جدول المستخدمين، اضغط على "⋮" بجانب المستخدم
2. اختر "تعديل"
3. عدّل البيانات المطلوبة
4. اضغط "تحديث"

#### إعادة تعيين كلمة المرور
1. في جدول المستخدمين، اضغط على "⋮" بجانب المستخدم
2. اختر "إعادة تعيين كلمة المرور"
3. أدخل كلمة المرور الجديدة
4. أكد كلمة المرور
5. اضغط "حفظ كلمة المرور"

#### قفل/إلغاء قفل حساب
1. في جدول المستخدمين، اضغط على "⋮" بجانب المستخدم
2. اختر "قفل المستخدم" أو "إلغاء القفل"

### مراقبة النشاط

#### عرض سجل العمليات
1. انتقل إلى "سجل العمليات" من الشريط الجانبي
2. استخدم الفلاتر للبحث:
   - المستخدم
   - نوع العملية
   - التاريخ
   - النتيجة (نجح/فشل)

#### مراجعة التنبيهات الأمنية
1. انتقل إلى "لوحة تحكم المدير"
2. اختر تبويب "الأمان"
3. راجع التنبيهات غير المؤكدة
4. اضغط "تأكيد" للتنبيهات المراجعة

### لوحة تحكم المدير

#### الوصول للوحة التحكم
1. انتقل إلى "لوحة تحكم المدير" من الشريط الجانبي
2. استعرض الإحصائيات في التبويبات:
   - **نظرة عامة**: إحصائيات عامة ومؤشرات الأداء
   - **المستخدمون**: تفاصيل المستخدمين والأدوار
   - **الأمان**: حالة الأمان والتنبيهات
   - **النظام**: معلومات تقنية وأداء

## الأدوار والصلاحيات

### الأدوار المتاحة

#### مدير النظام (admin)
- **الصلاحيات**: جميع الصلاحيات
- **الوصول**: جميع الوحدات والإعدادات
- **المسؤوليات**: إدارة المستخدمين، الأمان، النظام

#### مدير التخطيط (planning_manager)
- **الصلاحيات**: إدارة كاملة لقسم التخطيط
- **الوصول**: التخطيط، التقارير، لوحة التحكم
- **المسؤوليات**: جدولة الإنتاج، إدارة أوامر العمل

#### موظف التخطيط (planning_user)
- **الصلاحيات**: عمليات أساسية في التخطيط
- **الوصول**: التخطيط (عرض وإنشاء)، التقارير (عرض)
- **المسؤوليات**: إدخال أوامر العمل، متابعة الجدولة

#### مدير الإنتاج (production_manager)
- **الصلاحيات**: إدارة كاملة لقسم الإنتاج
- **الوصول**: الإنتاج، الجودة (عرض)، التقارير
- **المسؤوليات**: مراقبة الإنتاج، إدارة الآلات

#### موظف الإنتاج (production_user)
- **الصلاحيات**: عمليات أساسية في الإنتاج
- **الوصول**: الإنتاج (تسجيل البيانات)
- **المسؤوليات**: تسجيل بيانات الإنتاج، متابعة الآلات

#### مدير الجودة (quality_manager)
- **الصلاحيات**: إدارة كاملة لقسم الجودة
- **الوصول**: الجودة، التقارير، الإنتاج (عرض)
- **المسؤوليات**: إدارة فحوصات الجودة، تتبع العيوب

#### موظف الجودة (quality_user)
- **الصلاحيات**: عمليات أساسية في الجودة
- **الوصول**: الجودة (فحوصات)
- **المسؤوليات**: إجراء فحوصات الجودة، تسجيل النتائج

#### مدير المخزن (warehouse_manager)
- **الصلاحيات**: إدارة كاملة للمخزن
- **الوصول**: المخزن، التقارير
- **المسؤوليات**: إدارة المخزون، حركات المواد

#### موظف المخزن (warehouse_user)
- **الصلاحيات**: عمليات أساسية في المخزن
- **الوصول**: المخزن (تحديث المخزون)
- **المسؤوليات**: تسجيل حركات المخزون، جرد المواد

## نصائح مهمة

### الأمان
- **غيّر كلمة مرور المدير الافتراضية** فوراً
- **استخدم كلمات مرور قوية** (8 أحرف + أحرف كبيرة وصغيرة + أرقام)
- **راجع التنبيهات الأمنية** بانتظام
- **تحقق من سجل العمليات** للأنشطة المشبوهة

### إدارة المستخدمين
- **عيّن الأدوار بدقة** حسب المسؤوليات الفعلية
- **راجع صلاحيات المستخدمين** دورياً
- **احذف الحسابات غير المستخدمة**
- **استخدم أقسام واضحة** لتنظيم المستخدمين

### المراقبة
- **راجع لوحة تحكم المدير** يومياً
- **تابع إحصائيات النشاط** أسبوعياً
- **نظّف البيانات القديمة** شهرياً
- **اعمل نسخ احتياطية** للبيانات المهمة

## استكشاف الأخطاء

### مشاكل تسجيل الدخول
- **كلمة مرور خاطئة**: تحقق من كلمة المرور أو اطلب إعادة تعيين
- **حساب مقفل**: اتصل بالمدير لإلغاء القفل
- **حساب غير نشط**: اتصل بالمدير لتفعيل الحساب

### مشاكل الصلاحيات
- **عدم وصول لصفحة**: تحقق من الدور والصلاحيات المعينة
- **عدم ظهور خيارات**: قد تحتاج صلاحيات إضافية
- **رسائل خطأ**: راجع سجل العمليات للتفاصيل

### مشاكل الأداء
- **بطء في التحميل**: تحقق من اتصال الإنترنت
- **أخطاء في البيانات**: راجع وحدة التحكم للأخطاء التقنية
- **مشاكل في الحفظ**: تحقق من صحة البيانات المدخلة

## الدعم والمساعدة

### الحصول على المساعدة
1. **راجع هذا الدليل** للمشاكل الشائعة
2. **تحقق من سجل العمليات** لتفاصيل الأخطاء
3. **اتصل بمدير النظام** للمساعدة التقنية

### تقديم التغذية الراجعة
- **اقترح تحسينات** للواجهة أو الوظائف
- **أبلغ عن المشاكل** التقنية أو الأمنية
- **شارك تجربتك** في استخدام النظام

---

## ملاحظات مهمة

⚠️ **تحذير**: لا تشارك بيانات تسجيل الدخول مع أي شخص آخر

✅ **نصيحة**: احتفظ بنسخة من هذا الدليل للرجوع إليها عند الحاجة

🔒 **أمان**: غيّر كلمة المرور بانتظام واستخدم كلمات مرور قوية

📊 **مراقبة**: راجع التقارير والإحصائيات بانتظام لمتابعة الأداء
