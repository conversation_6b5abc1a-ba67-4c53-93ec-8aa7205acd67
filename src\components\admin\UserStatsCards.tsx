import { useMemo } from 'react';
import { User } from '@/contexts/LocalAuthContext';
import { Role } from '@/types/permissions';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  UserCheck, 
  UserX, 
  Shield, 
  Clock, 
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Activity
} from "lucide-react";

interface UserStatsCardsProps {
  users: User[];
  roles: Role[];
}

interface UserStats {
  total: number;
  active: number;
  inactive: number;
  locked: number;
  expired: number;
  mustChangePassword: number;
  recentLogins: number;
  neverLoggedIn: number;
  byRole: Record<string, number>;
  byDepartment: Record<string, number>;
}

export function UserStatsCards({ users, roles }: UserStatsCardsProps) {
  const stats = useMemo((): UserStats => {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const byRole: Record<string, number> = {};
    const byDepartment: Record<string, number> = {};
    
    let active = 0;
    let inactive = 0;
    let locked = 0;
    let expired = 0;
    let mustChangePassword = 0;
    let recentLogins = 0;
    let neverLoggedIn = 0;
    
    users.forEach(user => {
      // إحصائيات الحالة
      switch (user.status) {
        case 'active':
          active++;
          break;
        case 'inactive':
          inactive++;
          break;
        case 'locked':
          locked++;
          break;
        case 'expired':
          expired++;
          break;
      }
      
      // إحصائيات أخرى
      if (user.mustChangePassword) {
        mustChangePassword++;
      }
      
      if (user.lastLogin) {
        const lastLogin = new Date(user.lastLogin);
        if (lastLogin >= weekAgo) {
          recentLogins++;
        }
      } else {
        neverLoggedIn++;
      }
      
      // إحصائيات الأدوار
      byRole[user.role] = (byRole[user.role] || 0) + 1;
      
      // إحصائيات الأقسام
      byDepartment[user.department] = (byDepartment[user.department] || 0) + 1;
    });
    
    return {
      total: users.length,
      active,
      inactive,
      locked,
      expired,
      mustChangePassword,
      recentLogins,
      neverLoggedIn,
      byRole,
      byDepartment
    };
  }, [users]);

  const getRoleDisplayName = (roleId: string): string => {
    const role = roles.find(r => r.id === roleId);
    return role?.displayName || roleId;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* إجمالي المستخدمين */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي المستخدمين</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
          <p className="text-xs text-muted-foreground">
            {stats.active} نشط، {stats.inactive + stats.locked + stats.expired} غير نشط
          </p>
        </CardContent>
      </Card>

      {/* المستخدمون النشطون */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">المستخدمون النشطون</CardTitle>
          <UserCheck className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.active}</div>
          <p className="text-xs text-muted-foreground">
            {((stats.active / stats.total) * 100).toFixed(1)}% من المجموع
          </p>
        </CardContent>
      </Card>

      {/* المستخدمون المقفلون */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">المستخدمون المقفلون</CardTitle>
          <Shield className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{stats.locked}</div>
          <p className="text-xs text-muted-foreground">
            يحتاجون إلى إلغاء القفل
          </p>
        </CardContent>
      </Card>

      {/* تسجيلات الدخول الأخيرة */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">نشاط الأسبوع</CardTitle>
          <Activity className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">{stats.recentLogins}</div>
          <p className="text-xs text-muted-foreground">
            سجلوا الدخول خلال 7 أيام
          </p>
        </CardContent>
      </Card>

      {/* تحتاج تغيير كلمة المرور */}
      {stats.mustChangePassword > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تحتاج تغيير كلمة المرور</CardTitle>
            <AlertTriangle className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-600">{stats.mustChangePassword}</div>
            <p className="text-xs text-amber-700">
              يجب تغيير كلمة المرور في التسجيل التالي
            </p>
          </CardContent>
        </Card>
      )}

      {/* لم يسجلوا الدخول مطلقاً */}
      {stats.neverLoggedIn > 0 && (
        <Card className="border-gray-200 bg-gray-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">لم يسجلوا الدخول</CardTitle>
            <UserX className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.neverLoggedIn}</div>
            <p className="text-xs text-gray-700">
              لم يسجلوا الدخول مطلقاً
            </p>
          </CardContent>
        </Card>
      )}

      {/* توزيع الأدوار */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">توزيع الأدوار</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(stats.byRole)
              .sort(([, a], [, b]) => b - a)
              .map(([roleId, count]) => (
                <div key={roleId} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {getRoleDisplayName(roleId)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{count}</span>
                    <div className="w-16 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${(count / stats.total) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* توزيع الأقسام */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">توزيع الأقسام</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(stats.byDepartment)
              .sort(([, a], [, b]) => b - a)
              .slice(0, 5) // عرض أكبر 5 أقسام فقط
              .map(([department, count]) => (
                <div key={department} className="flex items-center justify-between">
                  <span className="text-sm">{department}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{count}</span>
                    <div className="w-16 bg-muted rounded-full h-2">
                      <div 
                        className="bg-secondary h-2 rounded-full" 
                        style={{ width: `${(count / stats.total) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* مؤشرات الأمان */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle className="text-sm font-medium">مؤشرات الأمان</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">
                {((stats.active / stats.total) * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-muted-foreground">معدل النشاط</div>
            </div>
            
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">
                {((stats.recentLogins / stats.total) * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-muted-foreground">نشاط أسبوعي</div>
            </div>
            
            <div className="text-center">
              <div className={`text-lg font-bold ${stats.locked > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {((stats.locked / stats.total) * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-muted-foreground">معدل القفل</div>
            </div>
            
            <div className="text-center">
              <div className={`text-lg font-bold ${stats.mustChangePassword > 0 ? 'text-amber-600' : 'text-green-600'}`}>
                {((stats.mustChangePassword / stats.total) * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-muted-foreground">تحتاج تحديث كلمة المرور</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
