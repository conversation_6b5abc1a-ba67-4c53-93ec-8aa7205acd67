import {
  ClipboardList,
  Factory,
  ShieldCheck,
  BarChart3,
  Bell,
  Calendar,
  Package,
  Users,
  Clock,
  Settings,
  LogOut,
  User,
  Factory,
  Wrench,
  FileText
} from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/LocalAuthContext";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

const menuItems = [
  {
    title: "لوحة التحكم",
    url: "/",
    icon: BarChart3,
    permissions: ["dashboard.view"]
  },
  {
    title: "التخطيط",
    url: "/planning",
    icon: ClipboardList,
    permissions: ["planning.view"]
  },
  {
    title: "الإنتاج",
    url: "/production",
    icon: Factory,
    permissions: ["production.view"]
  },
  {
    title: "الجودة",
    url: "/quality",
    icon: ShieldCheck,
    permissions: ["quality.view"]
  },
  {
    title: "المخزن",
    url: "/warehouse",
    icon: Package,
    permissions: ["warehouse.view"]
  },
  {
    title: "التقارير",
    url: "/reports",
    icon: BarChart3,
    permissions: ["reports.view"]
  },
  {
    title: "إدارة المنتجات",
    url: "/products",
    icon: Package,
    permissions: ["planning.manage"]
  },
  {
    title: "إدارة الورش",
    url: "/workshops",
    icon: Factory,
    permissions: ["production.manage"]
  },
  {
    title: "أوامر الشغل",
    url: "/work-orders",
    icon: FileText,
    permissions: ["planning.manage"]
  },
  {
    title: "المستخدمين",
    url: "/users",
    icon: Users,
    permissions: ["users.manage.all"]
  },
  {
    title: "سجل العمليات",
    url: "/audit-logs",
    icon: Clock,
    permissions: ["system.audit.view"]
  },
  {
    title: "لوحة تحكم المدير",
    url: "/admin",
    icon: Settings,
    permissions: ["users.manage.all"]
  },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const location = useLocation();
  const currentPath = location.pathname;
  const collapsed = state === "collapsed";
  const { currentUser, logout, hasAnyPermission } = useAuth();

  const isActive = (path: string) => currentPath === path;

  return (
    <Sidebar className={collapsed ? "w-14" : "w-64"}>
      <SidebarContent>
        <div className="p-4 border-b">
          <h2 className={`font-bold text-lg text-primary ${collapsed ? 'hidden' : 'block'}`}>
            نظام إدارة الإنتاج
          </h2>
        </div>
        
        <SidebarGroup>
          <SidebarGroupLabel className={collapsed ? 'hidden' : 'block'}>
            القوائم الرئيسية
          </SidebarGroupLabel>
          
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems
                .filter(item => {
                  // المدير يرى جميع القوائم
                  if (currentUser?.id === 'admin_001') return true;
                  // للمستخدمين الآخرين، تحقق من الصلاحيات
                  return !item.permissions || hasAnyPermission(item.permissions);
                })
                .map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={item.url}
                      className={({ isActive }) =>
                        `flex items-center p-3 rounded-lg transition-colors ${
                          isActive
                            ? 'bg-primary text-primary-foreground'
                            : 'hover:bg-accent'
                        }`
                      }
                    >
                      <item.icon className="h-5 w-5 ml-3" />
                      {!collapsed && <span className="text-sm font-medium">{item.title}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* معلومات المستخدم وتسجيل الخروج */}
        <div className="mt-auto p-4 border-t">
          {!collapsed && currentUser && (
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-primary-foreground" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">{currentUser.name}</div>
                  <div className="text-xs text-muted-foreground truncate">{currentUser.role}</div>
                </div>
              </div>
              <button
                onClick={logout}
                className="w-full flex items-center gap-2 p-2 text-sm text-destructive hover:bg-destructive/10 rounded-lg transition-colors"
              >
                <LogOut className="h-4 w-4" />
                تسجيل الخروج
              </button>
            </div>
          )}

          {collapsed && (
            <button
              onClick={logout}
              className="w-full flex items-center justify-center p-2 text-destructive hover:bg-destructive/10 rounded-lg transition-colors"
              title="تسجيل الخروج"
            >
              <LogOut className="h-4 w-4" />
            </button>
          )}
        </div>
      </SidebarContent>
    </Sidebar>
  );
}