import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { productionService } from '@/services/productionService';
import { WorkOrder, Workshop, Machine, WorkOrderBatch, Product } from '@/types/production';
import { 
  Factory, 
  Settings,
  Play,
  Pause,
  Clock,
  Users,
  Activity,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Package
} from "lucide-react";

export default function Production() {
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [workshops, setWorkshops] = useState<Workshop[]>([]);
  const [machines, setMachines] = useState<Machine[]>([]);
  const [activeBatches, setActiveBatches] = useState<WorkOrderBatch[]>([]);
  const [stats, setStats] = useState({
    activeWorkOrders: 0,
    totalBatches: 0,
    completedBatches: 0,
    inProgressBatches: 0,
    availableMachines: 0,
    busyMachines: 0,
    workshopEfficiency: 0
  });
  
  const { toast } = useToast();
  
  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const workOrdersData = productionService.getWorkOrders();
    const productsData = productionService.getProducts();
    const workshopsData = productionService.getWorkshops();
    const machinesData = productionService.getMachines();
    const statsData = productionService.getProductionStats();
    
    setWorkOrders(workOrdersData);
    setProducts(productsData);
    setWorkshops(workshopsData);
    setMachines(machinesData);
    
    // جمع جميع الدفعات النشطة
    const allBatches = workOrdersData.flatMap(wo => wo.batches);
    const activeBatchesData = allBatches.filter(batch => 
      batch.status === 'in_progress' || batch.status === 'pending'
    );
    setActiveBatches(activeBatchesData);
    
    setStats({
      activeWorkOrders: statsData.activeWorkOrders,
      totalBatches: allBatches.length,
      completedBatches: allBatches.filter(b => b.status === 'completed').length,
      inProgressBatches: allBatches.filter(b => b.status === 'in_progress').length,
      availableMachines: machinesData.filter(m => m.status === 'available').length,
      busyMachines: machinesData.filter(m => m.status === 'busy').length,
      workshopEfficiency: statsData.workshopEfficiency
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">في الانتظار</Badge>;
      case 'in_progress':
        return <Badge variant="default" className="bg-blue-600">قيد التنفيذ</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-600">مكتمل</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getWorkOrderByBatch = (batchId: string) => {
    return workOrders.find(wo => wo.batches.some(b => b.id === batchId));
  };

  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'غير محدد';
  };

  const getMachineStatus = (machineId: string) => {
    const machine = machines.find(m => m.id === machineId);
    return machine?.status || 'unknown';
  };

  const getWorkshopName = (workshopId: string) => {
    const workshop = workshops.find(w => w.id === workshopId);
    return workshop?.name || 'غير محدد';
  };

  const getMachineStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge variant="default" className="bg-green-600">متاحة</Badge>;
      case 'busy':
        return <Badge variant="default" className="bg-blue-600">مشغولة</Badge>;
      case 'maintenance':
        return <Badge variant="default" className="bg-yellow-600">صيانة</Badge>;
      case 'broken':
        return <Badge variant="destructive">معطلة</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">الإنتاج</h1>
          <p className="text-muted-foreground mt-2">
            مراقبة العمليات الإنتاجية والمكائن
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => window.location.href = '/workshops'}>
            <Factory className="ml-2 h-4 w-4" />
            إدارة الورش
          </Button>
          <Button variant="outline" onClick={() => window.location.href = '/work-orders'}>
            <Package className="ml-2 h-4 w-4" />
            أوامر الشغل
          </Button>
        </div>
      </div>

      {/* إحصائيات الإنتاج */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أوامر نشطة</CardTitle>
            <Activity className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.activeWorkOrders}</div>
            <p className="text-xs text-muted-foreground">
              أمر شغل قيد التنفيذ
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الدفعات</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBatches}</div>
            <p className="text-xs text-muted-foreground">
              {stats.inProgressBatches} قيد التنفيذ، {stats.completedBatches} مكتملة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المكائن</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.availableMachines + stats.busyMachines}</div>
            <p className="text-xs text-muted-foreground">
              {stats.availableMachines} متاحة، {stats.busyMachines} مشغولة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">كفاءة الورش</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.workshopEfficiency.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              متوسط الكفاءة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* الدفعات النشطة */}
      <Card>
        <CardHeader>
          <CardTitle>الدفعات قيد التنفيذ</CardTitle>
          <CardDescription>
            دفعات الإنتاج النشطة في الورش
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الدفعة</TableHead>
                <TableHead>أمر الشغل</TableHead>
                <TableHead>المنتج</TableHead>
                <TableHead>الكمية</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>المسؤول</TableHead>
                <TableHead>تاريخ البداية</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {activeBatches.map((batch) => {
                const workOrder = getWorkOrderByBatch(batch.id);
                return (
                  <TableRow key={batch.id}>
                    <TableCell className="font-mono">{batch.batchNumber}</TableCell>
                    <TableCell className="font-mono">{workOrder?.orderNumber}</TableCell>
                    <TableCell>{workOrder ? getProductName(workOrder.productId) : '-'}</TableCell>
                    <TableCell>{batch.quantity}</TableCell>
                    <TableCell>{getStatusBadge(batch.status)}</TableCell>
                    <TableCell>{batch.assignedTo || '-'}</TableCell>
                    <TableCell>
                      {batch.startDate ? new Date(batch.startDate).toLocaleDateString('ar-EG') : '-'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {batch.status === 'pending' && (
                          <Button variant="outline" size="sm" title="بدء التنفيذ">
                            <Play className="h-4 w-4" />
                          </Button>
                        )}
                        {batch.status === 'in_progress' && (
                          <Button variant="outline" size="sm" title="إيقاف مؤقت">
                            <Pause className="h-4 w-4" />
                          </Button>
                        )}
                        <Button variant="outline" size="sm" title="تفاصيل">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          
          {activeBatches.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد دفعات قيد التنفيذ حالياً</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* حالة المكائن */}
      <Card>
        <CardHeader>
          <CardTitle>حالة المكائن</CardTitle>
          <CardDescription>
            مراقبة حالة المكائن في جميع الورش
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {machines.map((machine) => (
              <Card key={machine.id} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Settings className="h-4 w-4 text-primary" />
                    <span className="font-medium">{machine.name}</span>
                  </div>
                  {getMachineStatusBadge(machine.status)}
                </div>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <div>الكود: {machine.code}</div>
                  <div>النوع: {machine.type}</div>
                  <div>الورشة: {getWorkshopName(machine.workshopId)}</div>
                  <div>السعة: {machine.capacity} قطعة/ساعة</div>
                </div>
              </Card>
            ))}
          </div>
          
          {machines.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد مكائن مسجلة</p>
              <Button className="mt-4" onClick={() => window.location.href = '/workshops'}>
                <Factory className="ml-2 h-4 w-4" />
                إضافة مكائن
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* حالة الورش */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {workshops.map((workshop) => {
          const workshopMachines = machines.filter(m => m.workshopId === workshop.id);
          const availableMachines = workshopMachines.filter(m => m.status === 'available').length;
          const busyMachines = workshopMachines.filter(m => m.status === 'busy').length;
          const maintenanceMachines = workshopMachines.filter(m => m.status === 'maintenance').length;
          
          return (
            <Card key={workshop.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Factory className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">{workshop.name}</CardTitle>
                  </div>
                  <Badge variant="outline">{workshop.code}</Badge>
                </div>
                <CardDescription>{workshop.location}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">المشرف:</span>
                    <span className="font-medium">{workshop.supervisor}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">العمال:</span>
                    <span className="font-medium">{workshop.currentLoad}/{workshop.capacity}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">المكائن:</span>
                    <div className="flex gap-1">
                      <Badge variant="default" className="bg-green-600 text-xs">
                        {availableMachines} متاحة
                      </Badge>
                      <Badge variant="default" className="bg-blue-600 text-xs">
                        {busyMachines} مشغولة
                      </Badge>
                      {maintenanceMachines > 0 && (
                        <Badge variant="default" className="bg-yellow-600 text-xs">
                          {maintenanceMachines} صيانة
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">ساعات العمل:</span>
                    <span className="font-medium">
                      {workshop.workingHours.start} - {workshop.workingHours.end}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
